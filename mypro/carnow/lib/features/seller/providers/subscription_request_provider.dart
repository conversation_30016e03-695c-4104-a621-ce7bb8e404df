import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../core/auth/simple_auth_system.dart';
import '../../../core/services/supabase_service.dart';
import '../../../core/errors/app_error.dart';
import '../models/subscription_request_model.dart';
import '../models/subscription_model.dart';
import '../models/seller_enums.dart';

part 'subscription_request_provider.g.dart';

final _logger = Logger('SubscriptionRequestProvider');

/// Provider لطلبات الاشتراك الحالية للبائع
@riverpod
class SellerSubscriptionRequest extends _$SellerSubscriptionRequest {
  @override
  Future<SubscriptionRequest?> build() async {
    final user = ref.watch(currentUserProvider);
    if (user == null) return null;

    return _fetchSellerRequest(user.id);
  }

  Future<SubscriptionRequest?> _fetchSellerRequest(String sellerId) async {
    try {
      _logger.info('Fetching subscription request for seller: $sellerId');

      final client = SupabaseService.instance.client;
      final response = await client
          .from('subscription_requests')
          .select()
          .eq('seller_id', sellerId)
          .or('status.eq.pending,status.eq.under_review')
          .order('created_at', ascending: false)
          .maybeSingle();

      if (response == null) {
        _logger.info(
          'No pending subscription request found for seller: $sellerId',
        );
        return null;
      }

      return SubscriptionRequest.fromJson(response);
    } catch (e, st) {
      _logger.severe(
        'Error fetching subscription request for seller: $sellerId',
        e,
        st,
      );
      throw AppError.database(
        message: 'فشل جلب طلب الاشتراك للبائع',
        originalError: e,
      );
    }
  }

  /// تقديم طلب اشتراك جديد
  Future<SubscriptionRequest> submitRequest({
    required SubscriptionPlan plan,
    required BillingCycle billingCycle,
    String? paymentMethodId,
    Map<String, dynamic>? businessDocuments,
  }) async {
    final user = ref.read(currentUserProvider);
    if (user == null) {
      throw const AppError.authentication(message: 'المستخدم غير مسجل');
    }

    try {
      _logger.info('Submitting subscription request for plan: ${plan.id}');

      final requestId = DateTime.now().millisecondsSinceEpoch.toString();
      final request = SubscriptionRequest(
        id: requestId,
        sellerId: user.id,
        planId: plan.id,
        requestedTier: plan.tier,
        billingCycle: billingCycle,
        status: SubscriptionRequestStatus.pending,
        requestDate: DateTime.now(),
        requestedPriceLD: billingCycle == BillingCycle.yearly
            ? plan.yearlyPriceLD
            : plan.monthlyPriceLD,
        paymentMethodId: paymentMethodId,
        businessDocuments: businessDocuments,
        priority: 3, // أولوية عادية - جميع الطلبات تُراجع خلال 48 ساعة
      );

      final client = SupabaseService.instance.client;
      await client.from('subscription_requests').insert(request.toJson());

      // تحديث الحالة المحلية
      state = AsyncValue.data(request);

      _logger.info('Subscription request submitted successfully: $requestId');
      return request;
    } catch (e, st) {
      _logger.severe('Error submitting subscription request', e, st);
      throw AppError.database(
        message: 'فشل تقديم طلب الاشتراك',
        originalError: e,
      );
    }
  }

  /// إلغاء طلب الاشتراك
  Future<void> cancelRequest() async {
    final currentRequest = state.value;
    if (currentRequest == null || !currentRequest.status.canCancel) {
      throw const AppError.validation(message: 'لا يمكن إلغاء هذا الطلب');
    }

    try {
      _logger.info('Cancelling subscription request: ${currentRequest.id}');

      final client = SupabaseService.instance.client;
      await client
          .from('subscription_requests')
          .update({'status': 'cancelled'})
          .eq('id', currentRequest.id);

      // تحديث الحالة المحلية
      state = const AsyncValue.data(null);

      _logger.info('Subscription request cancelled successfully');
    } catch (e, st) {
      _logger.severe('Error cancelling subscription request', e, st);
      throw AppError.database(
        message: 'فشل إلغاء طلب الاشتراك',
        originalError: e,
      );
    }
  }
}

/// Provider لقائمة طلبات الاشتراك للإدارة
@riverpod
class AdminSubscriptionRequests extends _$AdminSubscriptionRequests {
  @override
  Future<List<SubscriptionRequest>> build() async {
    return _fetchAllRequests();
  }

  Future<List<SubscriptionRequest>> _fetchAllRequests() async {
    try {
      _logger.info('Fetching all subscription requests for admin');

      final client = SupabaseService.instance.client;
      final response = await client
          .from('subscription_requests')
          .select()
          .order('created_at', ascending: false);

      return response.map(SubscriptionRequest.fromJson).toList();
    } catch (e, st) {
      _logger.severe('Error fetching subscription requests for admin', e, st);
      throw AppError.database(
        message: 'فشل جلب طلبات الاشتراك',
        originalError: e,
      );
    }
  }

  /// الموافقة على طلب اشتراك
  Future<void> approveRequest(String requestId, String adminId) async {
    try {
      _logger.info('Approving subscription request: $requestId');

      final client = SupabaseService.instance.client;

      // جلب تفاصيل الطلب قبل التحديث لاستخدامها في الإشعار
      final requestData = await client
          .from('subscription_requests')
          .select('*, seller_id')
          .eq('id', requestId)
          .single();

      final request = SubscriptionRequest.fromJson(requestData);
      final sellerId = request.sellerId;

      // تحديث حالة الطلب
      await client
          .from('subscription_requests')
          .update({
            'status': 'approved',
            'approved_date': DateTime.now().toIso8601String(),
            'admin_id': adminId,
          })
          .eq('id', requestId);

      // تحديث role المستخدم ليصبح بائعاً
      await _updateUserRoleToSeller(client, sellerId, adminId, request);

      // إنشاء اشتراك فعلي للبائع
      await _createActiveSubscription(requestId);

      // إرسال إشعار ترحيبي للبائع
      try {
        await _sendApprovalNotification(client, sellerId, request);
        _logger.info(
          'Approval notification sent successfully to seller: $sellerId',
        );
      } catch (notificationError) {
        _logger.warning(
          'Failed to send approval notification: $notificationError',
        );
        // لا نرمي خطأ هنا لأن الموافقة تمت بنجاح
      }

      // تحديث القائمة
      state = AsyncValue.data(await _fetchAllRequests());

      _logger.info('Subscription request approved successfully');
    } catch (e, st) {
      _logger.severe('Error approving subscription request', e, st);
      throw AppError.database(
        message: 'فشل الموافقة على طلب الاشتراك',
        originalError: e,
      );
    }
  }

  /// تحديث role المستخدم ليصبح بائعاً
  Future<void> _updateUserRoleToSeller(
    dynamic client,
    String sellerId,
    String adminId,
    SubscriptionRequest request,
  ) async {
    await client
        .from('profiles')
        .update({
          'role': 'seller',
          'seller_type': request.requestedTier.name,
          'is_approved': true,
          'approved_by': adminId,
          'approved_at': DateTime.now().toIso8601String(),
          'is_seller_requested': true,
          'updated_at': DateTime.now().toIso8601String(),
        })
        .eq('id', sellerId);

    _logger.info('User role updated to seller for user: $sellerId');
  }

  /// إرسال إشعار الموافقة للبائع
  Future<void> _sendApprovalNotification(
    dynamic client,
    String sellerId,
    SubscriptionRequest request,
  ) async {
    final tierName = _getTierDisplayName(request.requestedTier);
    final billingCycleName = request.billingCycle == BillingCycle.yearly
        ? 'سنوي'
        : 'شهري';

    await client.from('notifications').insert({
      'user_id': sellerId,
      'title': '🎉 مبروك! تم قبول طلب اشتراكك',
      'message':
          'تم قبول طلب اشتراكك في خطة "$tierName" ($billingCycleName). '
          'يمكنك الآن الاستفادة من جميع ميزات البائع المميز والبدء في عرض منتجاتك. '
          'مرحباً بك في عائلة البائعين المميزين!',
      'type': 'subscription_approved',
      'related_entity_id': request.id,
      'related_entity_type': 'subscription_request',
      'created_at': DateTime.now().toIso8601String(),
    });
  }

  /// حصول على اسم الخطة للعرض
  String _getTierDisplayName(SubscriptionTier tier) {
    switch (tier) {
      case SubscriptionTier.starter:
        return 'المبتدئ';
      case SubscriptionTier.basic:
        return 'الأساسي';
      case SubscriptionTier.premium:
        return 'المميز';
      case SubscriptionTier.anchor:
        return 'الأنكر';
      case SubscriptionTier.enterprise:
        return 'المؤسسي';
    }
  }

  /// رفض طلب اشتراك
  Future<void> rejectRequest(
    String requestId,
    String adminId,
    String rejectionReason,
  ) async {
    try {
      _logger.info('Rejecting subscription request: $requestId');

      final client = SupabaseService.instance.client;
      await client
          .from('subscription_requests')
          .update({
            'status': 'rejected',
            'rejected_date': DateTime.now().toIso8601String(),
            'admin_id': adminId,
            'rejection_reason': rejectionReason,
          })
          .eq('id', requestId);

      // تحديث القائمة
      state = AsyncValue.data(await _fetchAllRequests());

      _logger.info('Subscription request rejected successfully');
    } catch (e, st) {
      _logger.severe('Error rejecting subscription request', e, st);
      throw AppError.database(
        message: 'فشل رفض طلب الاشتراك',
        originalError: e,
      );
    }
  }

  /// تحديث أولوية الطلب
  Future<void> updateRequestPriority(String requestId, int priority) async {
    try {
      final client = SupabaseService.instance.client;
      await client
          .from('subscription_requests')
          .update({'priority': priority})
          .eq('id', requestId);

      // تحديث القائمة
      state = AsyncValue.data(await _fetchAllRequests());
    } catch (e, st) {
      _logger.severe('Error updating request priority', e, st);
      throw AppError.database(
        message: 'فشل تحديث أولوية الطلب',
        originalError: e,
      );
    }
  }

  /// إنشاء اشتراك فعلي بعد الموافقة
  Future<void> _createActiveSubscription(String requestId) async {
    final client = SupabaseService.instance.client;

    // جلب تفاصيل الطلب
    final requestData = await client
        .from('subscription_requests')
        .select()
        .eq('id', requestId)
        .single();

    final request = SubscriptionRequest.fromJson(requestData);

    // إنشاء اشتراك جديد
    final subscriptionId = DateTime.now().millisecondsSinceEpoch.toString();
    final subscription = SellerSubscription(
      id: subscriptionId,
      sellerId: request.sellerId,
      planId: request.planId,
      tier: request.requestedTier,
      billingCycle: request.billingCycle,
      status: SubscriptionStatus.active,
      startDate: DateTime.now(),
      endDate: _calculateEndDate(request.billingCycle),
      priceLD: request.requestedPriceLD,
      monthlyListingQuota: _getMonthlyQuota(request.requestedTier),
      additionalListingFeeLD: _getAdditionalFee(request.requestedTier),
      paymentMethodId: request.paymentMethodId,
      nextBillingDate: _calculateNextBillingDate(request.billingCycle),
    );

    await client.from('seller_subscriptions').insert(subscription.toJson());
  }

  DateTime _calculateEndDate(BillingCycle cycle) {
    final now = DateTime.now();
    return cycle == BillingCycle.yearly
        ? DateTime(now.year + 1, now.month, now.day)
        : DateTime(now.year, now.month + 1, now.day);
  }

  DateTime _calculateNextBillingDate(BillingCycle cycle) {
    final now = DateTime.now();
    return cycle == BillingCycle.yearly
        ? DateTime(now.year + 1, now.month, now.day)
        : DateTime(now.year, now.month + 1, now.day);
  }

  int _getMonthlyQuota(SubscriptionTier tier) {
    switch (tier) {
      case SubscriptionTier.starter:
        return 50;
      case SubscriptionTier.basic:
        return 250;
      case SubscriptionTier.premium:
        return 1000;
      case SubscriptionTier.anchor:
        return 5000;
      case SubscriptionTier.enterprise:
        return -1; // unlimited
    }
  }

  double _getAdditionalFee(SubscriptionTier tier) {
    switch (tier) {
      case SubscriptionTier.starter:
        return 1;
      case SubscriptionTier.basic:
        return 0.75;
      case SubscriptionTier.premium:
        return 0.5;
      case SubscriptionTier.anchor:
        return 0.25;
      case SubscriptionTier.enterprise:
        return 0;
    }
  }
}

/// Provider للطلبات المعلقة (pending) فقط
@riverpod
Future<List<SubscriptionRequest>> pendingSubscriptionRequests(Ref ref) async {
  final allRequests = await ref.watch(adminSubscriptionRequestsProvider.future);
  return allRequests.where((request) => request.status.isPending).toList();
}

/// Provider لعدد الطلبات المعلقة
@riverpod
Future<int> pendingRequestsCount(Ref ref) async {
  final pendingRequests = await ref.watch(
    pendingSubscriptionRequestsProvider.future,
  );
  return pendingRequests.length;
}
