import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../core/auth/simple_auth_system.dart';
import '../../../core/services/supabase_service.dart';
import '../models/subscription_model.dart';
import '../models/seller_enums.dart';

part 'subscription_provider.g.dart';

final _logger = Logger('SubscriptionProvider');

/// Provider for current seller subscription
@riverpod
class SellerSubscriptionProvider extends _$SellerSubscriptionProvider {
  @override
  Future<SellerSubscription?> build() async {
    final user = ref.watch(currentUserProvider);
    if (user == null) return null;

    return _fetchCurrentSubscription(user.id);
  }

  Future<SellerSubscription?> _fetchCurrentSubscription(String userId) async {
    try {
      _logger.info('Fetching current subscription for user: $userId');

      final client = SupabaseService.instance.client;
      final response = await client
          .from('seller_subscriptions')
          .select()
          .eq('seller_id', userId)
          .eq('status', 'active')
          .maybeSingle();

      if (response == null) {
        _logger.info('No active subscription found for user: $userId');
        return null;
      }

      return SellerSubscription.fromJson(response);
    } catch (e, st) {
      _logger.severe('Error fetching subscription for user: $userId', e, st);
      rethrow;
    }
  }

  Future<void> subscribeToPlan({
    required SubscriptionPlan plan,
    required BillingCycle billingCycle,
    String? paymentMethodId,
  }) async {
    final user = ref.read(currentUserProvider);
    if (user == null) throw Exception('User not authenticated');

    state = const AsyncValue.loading();

    try {
      _logger.info('Subscribing user ${user.id} to plan ${plan.id}');

      final client = SupabaseService.instance.client;
      final now = DateTime.now();

      final subscriptionData = {
        'seller_id': user.id,
        'plan_id': plan.id,
        'tier': plan.tier.name,
        'billing_cycle': billingCycle.name,
        'status': SubscriptionStatus.trial.name,
        'start_date': now.toIso8601String(),
        'trial_end_date': now
            .add(Duration(days: plan.trialDays))
            .toIso8601String(),
        'price_ld': billingCycle == BillingCycle.yearly
            ? plan.yearlyPriceLD
            : plan.monthlyPriceLD,
        'monthly_listing_quota': plan.monthlyListingQuota,
        'additional_listing_fee_ld': plan.additionalListingFeeLD,
        'auto_renewal': true,
        'has_priority_listing': plan.hasPriorityListing,
        'has_priority_support': plan.hasPrioritySupport,
        'has_dedicated_support': plan.hasDedicatedSupport,
        'payment_method_id': paymentMethodId,
      };

      await client.from('seller_subscriptions').insert(subscriptionData);

      // Refresh the state
      ref.invalidateSelf();

      _logger.info(
        'Successfully subscribed user ${user.id} to plan ${plan.id}',
      );
    } catch (e, st) {
      _logger.severe('Error subscribing to plan: ${plan.id}', e, st);
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }

  /// إلغاء الاشتراك مع الحفاظ على الفترة المتبقية (الطريقة الجديدة المحسنة)
  Future<SubscriptionCancellationResult> cancelSubscriptionGracefully({
    String? cancellationReason,
  }) async {
    final currentSubscription = await future;
    if (currentSubscription == null) {
      throw Exception('لا يوجد اشتراك نشط للإلغاء');
    }

    state = const AsyncValue.loading();

    try {
      _logger.info('Gracefully canceling subscription: ${currentSubscription.id}');

      final client = SupabaseService.instance.client;
      final now = DateTime.now();

      // حساب الفترة المتبقية
      final endDate = currentSubscription.endDate ?? 
          (currentSubscription.nextBillingDate ?? now.add(const Duration(days: 30)));
      
      final daysRemaining = endDate.difference(now).inDays.clamp(0, double.infinity).toInt();

      final updateData = {
        'status': SubscriptionStatus.canceledPendingExpiry.name,
        'auto_renewal': false,
        'cancellation_date': now.toIso8601String(),
        'cancellation_reason': cancellationReason,
        'grace_period_end': endDate.toIso8601String(),
        'updated_at': now.toIso8601String(),
      };

      await client
          .from('seller_subscriptions')
          .update(updateData)
          .eq('id', currentSubscription.id);

      // إرسال إشعار للبائع
      await _sendCancellationNotification(
        sellerId: currentSubscription.sellerId,
        daysRemaining: daysRemaining,
        endDate: endDate,
      );

      ref.invalidateSelf();

      _logger.info(
        'Successfully canceled subscription gracefully: ${currentSubscription.id}',
      );

      return SubscriptionCancellationResult(
        success: true,
        message: 'تم إلغاء الاشتراك بنجاح. يمكنك إدارة إعلاناتك الحالية لمدة $daysRemaining يوم إضافي',
        daysRemaining: daysRemaining,
        endDate: endDate,
        canManageExistingListings: true,
        canCreateNewListings: false,
      );
    } catch (e, st) {
      _logger.severe(
        'Error canceling subscription gracefully: ${currentSubscription.id}',
        e,
        st,
      );
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }

  /// الطريقة القديمة للإلغاء الفوري (للاستخدام الإداري فقط)
  Future<void> cancelSubscription() async {
    final currentSubscription = await future;
    if (currentSubscription == null) return;

    state = const AsyncValue.loading();

    try {
      _logger.info('Canceling subscription immediately: ${currentSubscription.id}');

      final client = SupabaseService.instance.client;
      await client
          .from('seller_subscriptions')
          .update({
            'status': SubscriptionStatus.canceled.name,
            'auto_renewal': false,
            'cancellation_date': DateTime.now().toIso8601String(),
          })
          .eq('id', currentSubscription.id);

      ref.invalidateSelf();

      _logger.info(
        'Successfully canceled subscription immediately: ${currentSubscription.id}',
      );
    } catch (e, st) {
      _logger.severe(
        'Error canceling subscription: ${currentSubscription.id}',
        e,
        st,
      );
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }

  /// إرسال إشعار إلغاء الاشتراك
  Future<void> _sendCancellationNotification({
    required String sellerId,
    required int daysRemaining,
    required DateTime endDate,
  }) async {
    try {
      final client = SupabaseService.instance.client;
      final notificationData = {
        'seller_id': sellerId,
        'type': 'subscription_cancelled',
        'title': 'تم إلغاء اشتراكك',
        'message': 'تم إلغاء اشتراكك بنجاح. يمكنك إدارة إعلاناتك الحالية حتى ${_formatDate(endDate)} ($daysRemaining يوم متبقي)',
        'data': {
          'cancellation_date': DateTime.now().toIso8601String(),
          'grace_period_end': endDate.toIso8601String(),
          'days_remaining': daysRemaining,
        },
        'read_at': null,
        'created_at': DateTime.now().toIso8601String(),
      };

      await client.from('seller_notifications').insert(notificationData);
    } catch (e) {
      _logger.warning('Failed to send cancellation notification: $e');
      // لا نرمي خطأ هنا لأن الإلغاء تم بنجاح
    }
  }

  /// تنسيق التاريخ للعرض
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> updateAutoRenewal(bool autoRenewal) async {
    final currentSubscription = await future;
    if (currentSubscription == null) return;

    try {
      final client = SupabaseService.instance.client;
      await client
          .from('seller_subscriptions')
          .update({'auto_renewal': autoRenewal})
          .eq('id', currentSubscription.id);

      ref.invalidateSelf();
    } catch (e, st) {
      _logger.severe('Error updating auto-renewal', e, st);
      rethrow;
    }
  }

  Future<void> upgradePlan({
    required SubscriptionPlan newPlan,
    required bool immediate,
  }) async {
    final currentSubscription = await future;
    if (currentSubscription == null) {
      throw Exception('No active subscription found');
    }

    state = const AsyncValue.loading();

    try {
      _logger.info(
        'Upgrading subscription: ${currentSubscription.id} to plan: ${newPlan.id}',
      );

      final client = SupabaseService.instance.client;

      final updateData = {
        'plan_id': newPlan.id,
        'tier': newPlan.tier.name,
        'price_ld': currentSubscription.billingCycle == BillingCycle.yearly
            ? newPlan.yearlyPriceLD
            : newPlan.monthlyPriceLD,
        'monthly_listing_quota': newPlan.monthlyListingQuota,
        'additional_listing_fee_ld': newPlan.additionalListingFeeLD,
        'has_priority_listing': newPlan.hasPriorityListing,
        'has_priority_support': newPlan.hasPrioritySupport,
        'has_dedicated_support': newPlan.hasDedicatedSupport,
      };

      if (immediate) {
        // For upgrades, apply immediately
        await client
            .from('seller_subscriptions')
            .update(updateData)
            .eq('id', currentSubscription.id);
      } else {
        // For downgrades, schedule for next billing cycle
        updateData['status'] = 'pending_change';
        updateData['pending_plan_id'] = newPlan.id;

        await client
            .from('seller_subscriptions')
            .update(updateData)
            .eq('id', currentSubscription.id);
      }

      ref.invalidateSelf();

      _logger.info(
        'Successfully upgraded subscription: ${currentSubscription.id}',
      );
    } catch (e, st) {
      _logger.severe(
        'Error upgrading subscription: ${currentSubscription.id}',
        e,
        st,
      );
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }
}

/// Provider for subscription billing history
@riverpod
class SubscriptionBillingHistory extends _$SubscriptionBillingHistory {
  @override
  Future<List<SubscriptionBilling>> build() async {
    final user = ref.watch(currentUserProvider);
    if (user == null) return [];

    return _fetchBillingHistory(user.id);
  }

  Future<List<SubscriptionBilling>> _fetchBillingHistory(String userId) async {
    try {
      _logger.info('Fetching billing history for user: $userId');

      final client = SupabaseService.instance.client;
      final response = await client
          .from('subscription_billing')
          .select()
          .eq('seller_id', userId)
          .order('billing_date', ascending: false);

      return response.map(SubscriptionBilling.fromJson).toList();
    } catch (e, st) {
      _logger.severe('Error fetching billing history for user: $userId', e, st);
      rethrow;
    }
  }

  Future<void> refresh() async {
    ref.invalidateSelf();
  }
}

/// Provider for available subscription plans
@riverpod
Future<List<SubscriptionPlan>> availableSubscriptionPlans(Ref ref) async {
  try {
    _logger.info('Fetching available subscription plans');

    // For now, return predefined plans
    // In production, this could fetch from database
    return PredefinedPlans.allPlans.where((plan) => plan.isActive).toList();
  } catch (e, st) {
    _logger.severe('Error fetching subscription plans', e, st);
    rethrow;
  }
}

/// Provider for monthly quota usage
@riverpod
class MonthlyQuotaUsageProvider extends _$MonthlyQuotaUsageProvider {
  @override
  Future<MonthlyQuotaUsage?> build() async {
    final user = ref.watch(currentUserProvider);
    if (user == null) return null;

    return _fetchQuotaUsage(user.id);
  }

  Future<MonthlyQuotaUsage?> _fetchQuotaUsage(String userId) async {
    try {
      _logger.info('Fetching quota usage for user: $userId');

      final client = SupabaseService.instance.client;
      final now = DateTime.now();
      final response = await client
          .from('monthly_quota_usage')
          .select()
          .eq('seller_id', userId)
          .eq('month', now.month)
          .eq('year', now.year)
          .maybeSingle();

      if (response == null) return null;

      return MonthlyQuotaUsage.fromJson(response);
    } catch (e, st) {
      _logger.severe('Error fetching quota usage for user: $userId', e, st);
      rethrow;
    }
  }

  Future<void> refresh() async {
    ref.invalidateSelf();
  }
}
