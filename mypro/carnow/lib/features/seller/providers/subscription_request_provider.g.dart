// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_request_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pendingSubscriptionRequestsHash() =>
    r'd18d81b0d2402620aa5867fd6002ea630b91064e';

/// Provider للطلبات المعلقة (pending) فقط
///
/// Copied from [pendingSubscriptionRequests].
@ProviderFor(pendingSubscriptionRequests)
final pendingSubscriptionRequestsProvider =
    AutoDisposeFutureProvider<List<SubscriptionRequest>>.internal(
      pendingSubscriptionRequests,
      name: r'pendingSubscriptionRequestsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$pendingSubscriptionRequestsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PendingSubscriptionRequestsRef =
    AutoDisposeFutureProviderRef<List<SubscriptionRequest>>;
String _$pendingRequestsCountHash() =>
    r'955aa65388e6f288287588dcb453eb5952ad7634';

/// Provider لعدد الطلبات المعلقة
///
/// Copied from [pendingRequestsCount].
@ProviderFor(pendingRequestsCount)
final pendingRequestsCountProvider = AutoDisposeFutureProvider<int>.internal(
  pendingRequestsCount,
  name: r'pendingRequestsCountProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$pendingRequestsCountHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PendingRequestsCountRef = AutoDisposeFutureProviderRef<int>;
String _$sellerSubscriptionRequestHash() =>
    r'd1e038febe588572afb1d150ab5e29e60f1d88bb';

/// Provider لطلبات الاشتراك الحالية للبائع
///
/// Copied from [SellerSubscriptionRequest].
@ProviderFor(SellerSubscriptionRequest)
final sellerSubscriptionRequestProvider =
    AutoDisposeAsyncNotifierProvider<
      SellerSubscriptionRequest,
      SubscriptionRequest?
    >.internal(
      SellerSubscriptionRequest.new,
      name: r'sellerSubscriptionRequestProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sellerSubscriptionRequestHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SellerSubscriptionRequest =
    AutoDisposeAsyncNotifier<SubscriptionRequest?>;
String _$adminSubscriptionRequestsHash() =>
    r'0c55be0a0ea038bd8461ac0956dfac69baa53301';

/// Provider لقائمة طلبات الاشتراك للإدارة
///
/// Copied from [AdminSubscriptionRequests].
@ProviderFor(AdminSubscriptionRequests)
final adminSubscriptionRequestsProvider =
    AutoDisposeAsyncNotifierProvider<
      AdminSubscriptionRequests,
      List<SubscriptionRequest>
    >.internal(
      AdminSubscriptionRequests.new,
      name: r'adminSubscriptionRequestsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$adminSubscriptionRequestsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AdminSubscriptionRequests =
    AutoDisposeAsyncNotifier<List<SubscriptionRequest>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
