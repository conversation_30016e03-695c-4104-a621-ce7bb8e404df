import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';

import '../../../core/services/analytics_service.dart';
import '../services/subscription_analytics_api_service.dart';
import '../models/seller_enums.dart';

part 'subscription_analytics_provider.g.dart';

final _logger = Logger('SubscriptionAnalyticsProvider');

/// Subscription-specific analytics service that tracks user interactions
/// related to subscription management, billing, and plan changes
class SubscriptionAnalyticsService {
  SubscriptionAnalyticsService(this._analytics, this._apiService);

  final AnalyticsService _analytics;
  final SubscriptionAnalyticsApiService _apiService;

  // === Subscription Plan Analytics ===

  /// Track when user views subscription plans
  Future<void> trackPlanViewEvent({
    String? currentPlan,
    String? billingCycle,
    Map<String, dynamic>? context,
  }) async {
    // Track locally for analytics
    await _analytics.logEvent(
      'subscription_plans_viewed',
      properties: {
        'current_plan': currentPlan,
        'billing_cycle': billingCycle,
        'timestamp': DateTime.now().toIso8601String(),
        ...?context,
      },
    );

    // Track via API for backend analytics
    await _apiService.trackPlanViewEvent(
      currentPlan: currentPlan,
      billingCycle: billingCycle,
      context: context,
    );
  }

  /// Track plan selection/change
  Future<void> trackPlanSelectionEvent({
    required String selectedPlan,
    String? previousPlan,
    required String billingCycle,
    required double price,
    Map<String, dynamic>? context,
  }) async {
    // Track locally for analytics
    await _analytics.logEvent(
      'subscription_plan_selected',
      properties: {
        'selected_plan': selectedPlan,
        'previous_plan': previousPlan,
        'billing_cycle': billingCycle,
        'price': price,
        'is_upgrade': _isUpgrade(previousPlan, selectedPlan),
        'is_downgrade': _isDowngrade(previousPlan, selectedPlan),
        'timestamp': DateTime.now().toIso8601String(),
        ...?context,
      },
    );

    // Track via API for backend analytics
    await _apiService.trackPlanSelectionEvent(
      selectedPlan: selectedPlan,
      previousPlan: previousPlan,
      billingCycle: billingCycle,
      price: price,
      context: context,
    );
  }

  /// Track successful subscription creation/upgrade
  Future<void> trackSubscriptionChangeEvent({
    required String action, // 'created', 'upgraded', 'downgraded', 'cancelled'
    required String planId,
    String? previousPlanId,
    required double amount,
    required String billingCycle,
    Map<String, dynamic>? context,
  }) async {
    await _analytics.logEvent(
      'subscription_$action',
      properties: {
        'plan_id': planId,
        'previous_plan_id': previousPlanId,
        'amount': amount,
        'billing_cycle': billingCycle,
        'timestamp': DateTime.now().toIso8601String(),
        ...?context,
      },
    );

    // Store subscription analytics via API
    try {
      switch (action) {
        case 'upgraded':
          if (previousPlanId != null) {
            await _apiService.trackSubscriptionUpgradeEvent(
              fromPlan: previousPlanId,
              toPlan: planId,
              priceDifference: amount,
              context: context,
            );
          }
          break;
        case 'cancelled':
          await _apiService.trackSubscriptionCancellationEvent(
            plan: planId,
            reason: context?['reason'] ?? 'user_requested',
            context: context,
          );
          break;
        case 'created':
        case 'downgraded':
          await _apiService.trackBillingEvent(
            eventType: action,
            amount: amount,
            currency: 'SAR',
            context: context,
          );
          break;
      }
      _logger.info('Subscription analytics stored: $action for plan $planId');
    } catch (e) {
      _logger.severe('Failed to store subscription analytics: $e');
    }
  }

  // === Billing Analytics ===

  /// Track billing history page views
  Future<void> trackBillingHistoryViewEvent({
    int? invoiceCount,
    double? totalAmount,
    Map<String, dynamic>? context,
  }) async {
    await _analytics.logEvent(
      'billing_history_viewed',
      properties: {
        'invoice_count': invoiceCount,
        'total_amount': totalAmount,
        'timestamp': DateTime.now().toIso8601String(),
        ...?context,
      },
    );
  }

  /// Track billing-related actions
  Future<void> trackBillingActionEvent({
    required String
    action, // 'download_invoice', 'retry_payment', 'view_invoice'
    required String invoiceId,
    double? amount,
    String? status,
    Map<String, dynamic>? context,
  }) async {
    await _analytics.logEvent(
      'billing_$action',
      properties: {
        'invoice_id': invoiceId,
        'amount': amount,
        'status': status,
        'timestamp': DateTime.now().toIso8601String(),
        ...?context,
      },
    );
  }

  // === Payment Methods Analytics ===

  /// Track payment methods management
  Future<void> trackPaymentMethodEvent({
    required String action, // 'added', 'removed', 'updated', 'set_default'
    required String paymentMethodType, // 'credit_card', 'paypal', etc.
    Map<String, dynamic>? context,
  }) async {
    await _analytics.logEvent(
      'payment_method_$action',
      properties: {
        'payment_method_type': paymentMethodType,
        'timestamp': DateTime.now().toIso8601String(),
        ...?context,
      },
    );
  }

  // === Usage Analytics ===

  /// Track quota usage views
  Future<void> trackQuotaUsageViewEvent({
    required SubscriptionTier tier,
    required int usedListings,
    required int totalListings,
    required int usedFeaturedListings,
    required int totalFeaturedListings,
    Map<String, dynamic>? context,
  }) async {
    final usagePercentage = totalListings > 0
        ? (usedListings / totalListings * 100)
        : 0;
    final featuredUsagePercentage = totalFeaturedListings > 0
        ? (usedFeaturedListings / totalFeaturedListings * 100)
        : 0;

    await _analytics.logEvent(
      'quota_usage_viewed',
      properties: {
        'tier': tier.name,
        'used_listings': usedListings,
        'total_listings': totalListings,
        'usage_percentage': usagePercentage,
        'used_featured_listings': usedFeaturedListings,
        'total_featured_listings': totalFeaturedListings,
        'featured_usage_percentage': featuredUsagePercentage,
        'is_near_limit': usagePercentage > 80,
        'timestamp': DateTime.now().toIso8601String(),
        ...?context,
      },
    );
  }

  // === Conversion Analytics ===

  /// Track conversion funnel events
  Future<void> trackConversionFunnelEvent({
    required String
    stage, // 'viewed_plans', 'selected_plan', 'entered_payment', 'completed_payment'
    String? planId,
    double? amount,
    Map<String, dynamic>? context,
  }) async {
    await _analytics.logEvent(
      'subscription_funnel_$stage',
      properties: {
        'plan_id': planId,
        'amount': amount,
        'timestamp': DateTime.now().toIso8601String(),
        ...?context,
      },
    );
  }

  /// Track abandonment events
  Future<void> trackAbandonmentEvent({
    required String
    stage, // 'plan_selection', 'payment_entry', 'payment_processing'
    String? planId,
    String? reason, // 'back_button', 'timeout', 'error'
    Map<String, dynamic>? context,
  }) async {
    await _analytics.logEvent(
      'subscription_abandoned',
      properties: {
        'stage': stage,
        'plan_id': planId,
        'reason': reason,
        'timestamp': DateTime.now().toIso8601String(),
        ...?context,
      },
    );
  }

  // === Error Analytics ===

  /// Track subscription-related errors
  Future<void> trackSubscriptionErrorEvent({
    required String
    errorType, // 'payment_failed', 'api_error', 'validation_error'
    required String errorMessage,
    String? planId,
    String? context,
    Map<String, dynamic>? metadata,
  }) async {
    await _analytics.logEvent(
      'subscription_error',
      properties: {
        'error_type': errorType,
        'error_message': errorMessage,
        'plan_id': planId,
        'context': context,
        'timestamp': DateTime.now().toIso8601String(),
        ...?metadata,
      },
    );

    _logger.severe(
      'Subscription Error: $errorMessage',
      {
        'error_type': errorType,
        'plan_id': planId ?? 'unknown',
        'context': context ?? 'unknown',
        ...?metadata,
      },
    );
  }

  // === Helper Methods ===

  bool _isUpgrade(String? previousPlan, String selectedPlan) {
    final planHierarchy = {
      'starter': 1,
      'basic': 2,
      'premium': 3,
      'anchor': 4,
      'enterprise': 5,
    };

    if (previousPlan == null) return false;

    final previousLevel = planHierarchy[previousPlan] ?? 0;
    final selectedLevel = planHierarchy[selectedPlan] ?? 0;

    return selectedLevel > previousLevel;
  }

  bool _isDowngrade(String? previousPlan, String selectedPlan) {
    final planHierarchy = {
      'starter': 1,
      'basic': 2,
      'premium': 3,
      'anchor': 4,
      'enterprise': 5,
    };

    if (previousPlan == null) return false;

    final previousLevel = planHierarchy[previousPlan] ?? 0;
    final selectedLevel = planHierarchy[selectedPlan] ?? 0;

    return selectedLevel < previousLevel;
  }

  // === Aggregated Analytics Methods ===

  /// Get subscription analytics summary
  Future<Map<String, dynamic>> getSubscriptionAnalyticsSummary() async {
    try {
      return await _apiService.getSubscriptionAnalyticsSummary();
    } catch (e) {
      _logger.severe('Failed to get subscription analytics summary: $e');
      return {};
    }
  }
}

@riverpod
SubscriptionAnalyticsService subscriptionAnalytics(Ref ref) {
  final analytics = AnalyticsService();
  final apiService = ref.watch(subscriptionAnalyticsApiServiceProvider);
  return SubscriptionAnalyticsService(analytics, apiService);
}
