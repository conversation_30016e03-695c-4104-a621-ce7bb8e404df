import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/simple_analytics_model.dart';
import '../../../core/networking/simple_api_client.dart';
import '../../../core/auth/simple_auth_system.dart';

part 'simple_analytics_provider.g.dart';

/// Simple analytics provider - simplified
@riverpod
Future<SimpleAnalyticsModel> simpleAnalytics(
  Ref ref,
  String period,
) async {
  final apiClient = ref.read(simpleApiClientProvider);
  final authSystem = ref.read(simpleAuthSystemProvider);
  
  final user = authSystem.user;
  if (user == null) {
    throw Exception('User not authenticated');
  }

  try {
    // Fetch analytics data from Go backend
    final response = await apiClient.getApi(
      '/analytics/seller/${user.id}',
      queryParameters: {'period': period},
    );

    return SimpleAnalyticsModel.fromJson(response.data);
  } catch (e) {
    // Fallback to mock data if backend is not available
    return _generateMockAnalytics(period);
  }
}

/// Generate mock analytics data for fallback
SimpleAnalyticsModel _generateMockAnalytics(String period) {
  final now = DateTime.now();
  
  return SimpleAnalyticsModel(
    salesData: SalesData(
      totalRevenue: 15000.0,
      previousRevenue: 12000.0,
      totalOrders: 45,
      previousOrders: 38,
      averageOrderValue: 333.33,
      conversionRate: 4.5,
      dailyData: [],
      topDays: {},
    ),
    productData: ProductData(
      totalProducts: 25,
      activeProducts: 22,
      topProducts: [],
      lowStockProducts: [],
      categoryPerformance: {
        'auto_parts': 45.0,
        'accessories': 30.0,
        'electronics': 25.0,
      },
    ),
    customerData: CustomerData(
      totalCustomers: 120,
      newCustomers: 15,
      returningCustomers: 105,
      averageRating: 4.5,
      topCustomers: [],
    ),
    recommendations: [
      Recommendation(
        id: '1',
        title: 'إعادة تخزين المنتجات الشائعة',
        description: 'Consider restocking popular items',
        type: RecommendationType.inventory,
        priority: RecommendationPriority.high,
        actionItems: ['Check inventory levels', 'Order new stock'],
      ),
      Recommendation(
        id: '2',
        title: 'استراتيجيات الاحتفاظ بالعملاء',
        description: 'Focus on customer retention strategies',
        type: RecommendationType.customer,
        priority: RecommendationPriority.medium,
        actionItems: ['Send follow-up emails', 'Create loyalty program'],
      ),
      Recommendation(
        id: '3',
        title: 'تحسين هوامش التسعير',
        description: 'Optimize pricing for better margins',
        type: RecommendationType.pricing,
        priority: RecommendationPriority.medium,
        actionItems: ['Analyze competitor prices', 'Review cost structure'],
      ),
    ],
    lastUpdated: now,
    period: period,
  );
}



/// مزود فترات التحليل المتاحة
final analyticsPeriodsProvider = Provider<List<AnalyticsPeriod>>((ref) {
  return [
    const AnalyticsPeriod(key: 'week', label: 'الأسبوع الماضي', icon: 'week'),
    const AnalyticsPeriod(key: 'month', label: 'الشهر الماضي', icon: 'month'),
    const AnalyticsPeriod(
      key: 'quarter',
      label: 'الربع الأخير',
      icon: 'quarter',
    ),
    const AnalyticsPeriod(key: 'year', label: 'السنة الماضية', icon: 'year'),
  ];
});

/// نموذج فترة التحليل
class AnalyticsPeriod {
  const AnalyticsPeriod({
    required this.key,
    required this.label,
    required this.icon,
  });

  final String key;
  final String label;
  final String icon;
}
