// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_analytics_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$subscriptionAnalyticsHash() =>
    r'd55ad5972cfddc208dd3d79b674336f65884a0eb';

/// See also [subscriptionAnalytics].
@ProviderFor(subscriptionAnalytics)
final subscriptionAnalyticsProvider =
    AutoDisposeProvider<SubscriptionAnalyticsService>.internal(
      subscriptionAnalytics,
      name: r'subscriptionAnalyticsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$subscriptionAnalyticsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SubscriptionAnalyticsRef =
    AutoDisposeProviderRef<SubscriptionAnalyticsService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
