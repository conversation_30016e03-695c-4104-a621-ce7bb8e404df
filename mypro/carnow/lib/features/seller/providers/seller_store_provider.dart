import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../core/services/supabase_service.dart';
import '../models/seller_store_model.dart';

final _logger = Logger('SellerStoreProvider');

/// Provider that fetches a seller's store by seller ID
final sellerStoreProvider = FutureProvider.family<SellerStoreModel?, int>((
  ref,
  sellerId,
) async {
  try {
    final response = await SupabaseService.instance.client
        .from('seller_stores')
        .select()
        .eq('seller_id', sellerId)
        .single();

    return SellerStoreModel.fromJson(response);
  } catch (e) {
    // If no store is found, return null instead of throwing
    if (e.toString().contains('No rows found')) {
      return null;
    }
    _logger.severe('Error fetching seller store: $e');
    rethrow;
  }
});

/// Notifier for managing seller store
class SellerStoreNotifier extends AsyncNotifier<SellerStoreModel?> {
  @override
  Future<SellerStoreModel?> build() async {
    // Initial state is null until explicitly loaded
    return null;
  }

  /// Load store for a seller
  Future<void> loadStore(int sellerId) async {
    state = const AsyncValue.loading();

    try {
      final response = await SupabaseService.instance.client
          .from('seller_stores')
          .select()
          .eq('seller_id', sellerId)
          .single();

      state = AsyncValue.data(SellerStoreModel.fromJson(response));
    } catch (e) {
      if (e.toString().contains('No rows found')) {
        state = const AsyncValue.data(null);
      } else {
        _logger.severe('Error loading seller store: $e');
        state = AsyncValue.error(e, StackTrace.current);
      }
    }
  }

  /// Create a new store
  Future<SellerStoreModel> createStore(SellerStoreModel store) async {
    try {
      final response = await SupabaseService.instance.client
          .from('seller_stores')
          .insert(store.toJson())
          .select()
          .single();

      final createdStore = SellerStoreModel.fromJson(response);
      state = AsyncValue.data(createdStore);
      return createdStore;
    } catch (e) {
      _logger.severe('Error creating store: $e');
      rethrow;
    }
  }

  /// Update an existing store
  Future<SellerStoreModel> updateStore(SellerStoreModel store) async {
    if (store.id == null) {
      throw ArgumentError('Cannot update store with null ID');
    }

    try {
      final response = await SupabaseService.instance.client
          .from('seller_stores')
          .update(store.toJson())
          .eq('id', store.id!)
          .select()
          .single();

      final updatedStore = SellerStoreModel.fromJson(response);
      state = AsyncValue.data(updatedStore);
      return updatedStore;
    } catch (e) {
      _logger.severe('Error updating store: $e');
      rethrow;
    }
  }
}

/// Provider for the seller store notifier
final sellerStoreNotifierProvider =
    AsyncNotifierProvider<SellerStoreNotifier, SellerStoreModel?>(
      SellerStoreNotifier.new,
    );
