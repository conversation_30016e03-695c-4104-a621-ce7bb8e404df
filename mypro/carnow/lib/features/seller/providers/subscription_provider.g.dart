// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$availableSubscriptionPlansHash() =>
    r'93cb2aba92f13644c57596054a2ead7e7b8db960';

/// Provider for available subscription plans
///
/// Copied from [availableSubscriptionPlans].
@ProviderFor(availableSubscriptionPlans)
final availableSubscriptionPlansProvider =
    AutoDisposeFutureProvider<List<SubscriptionPlan>>.internal(
      availableSubscriptionPlans,
      name: r'availableSubscriptionPlansProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$availableSubscriptionPlansHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AvailableSubscriptionPlansRef =
    AutoDisposeFutureProviderRef<List<SubscriptionPlan>>;
String _$sellerSubscriptionProviderHash() =>
    r'eb6b24f83c09e7c605095ac3d52e5f0b0dc15096';

/// Provider for current seller subscription
///
/// Copied from [SellerSubscriptionProvider].
@ProviderFor(SellerSubscriptionProvider)
final sellerSubscriptionProviderProvider =
    AutoDisposeAsyncNotifierProvider<
      SellerSubscriptionProvider,
      SellerSubscription?
    >.internal(
      SellerSubscriptionProvider.new,
      name: r'sellerSubscriptionProviderProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sellerSubscriptionProviderHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SellerSubscriptionProvider =
    AutoDisposeAsyncNotifier<SellerSubscription?>;
String _$subscriptionBillingHistoryHash() =>
    r'8330e42b13e63c0abd70ff4af542742753711f56';

/// Provider for subscription billing history
///
/// Copied from [SubscriptionBillingHistory].
@ProviderFor(SubscriptionBillingHistory)
final subscriptionBillingHistoryProvider =
    AutoDisposeAsyncNotifierProvider<
      SubscriptionBillingHistory,
      List<SubscriptionBilling>
    >.internal(
      SubscriptionBillingHistory.new,
      name: r'subscriptionBillingHistoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$subscriptionBillingHistoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SubscriptionBillingHistory =
    AutoDisposeAsyncNotifier<List<SubscriptionBilling>>;
String _$monthlyQuotaUsageProviderHash() =>
    r'3c8d1c2716b28afad125099c428fb520423dcc13';

/// Provider for monthly quota usage
///
/// Copied from [MonthlyQuotaUsageProvider].
@ProviderFor(MonthlyQuotaUsageProvider)
final monthlyQuotaUsageProviderProvider =
    AutoDisposeAsyncNotifierProvider<
      MonthlyQuotaUsageProvider,
      MonthlyQuotaUsage?
    >.internal(
      MonthlyQuotaUsageProvider.new,
      name: r'monthlyQuotaUsageProviderProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$monthlyQuotaUsageProviderHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$MonthlyQuotaUsageProvider =
    AutoDisposeAsyncNotifier<MonthlyQuotaUsage?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
