// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'seller_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sellerRepositoryHash() => r'de8b738926d5795aa2120310ce9b9d0fb212f3ff';

/// مزود لمستودع البائع
///
/// Copied from [sellerRepository].
@ProviderFor(sellerRepository)
final sellerRepositoryProvider = AutoDisposeProvider<SellerRepository>.internal(
  sellerRepository,
  name: r'sellerRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$sellerRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SellerRepositoryRef = AutoDisposeProviderRef<SellerRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
