import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/services/supabase_service.dart';
import '../models/seller_profile_model.dart';

part 'seller_repository.g.dart';

/// مستودع لإدارة بيانات البائع
class SellerRepository {
  /// الحصول على ملف البائع الشخصي بواسطة رقم المستخدم
  Future<SellerProfile?> getSellerProfile(int userId) async {
    try {
      final response = await SupabaseService.instance.client
          .from('seller_profiles')
          .select()
          .eq('user_id', userId)
          .maybeSingle();

      if (response == null) return null;

      return SellerProfile.fromJson(response);
    } catch (e) {
      // التعامل مع الخطأ
      return null;
    }
  }

  /// تحديث أو إنشاء ملف البائع الشخصي
  Future<SellerProfile?> upsertSellerProfile(SellerProfile profile) async {
    try {
      final response = await SupabaseService.instance.client
          .from('seller_profiles')
          .upsert(profile.toJson())
          .select()
          .single();

      return SellerProfile.fromJson(response);
    } catch (e) {
      // التعامل مع الخطأ
      return null;
    }
  }
}

/// مزود لمستودع البائع
@riverpod
SellerRepository sellerRepository(Ref ref) => SellerRepository();
