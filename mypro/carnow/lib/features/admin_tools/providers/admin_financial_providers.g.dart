// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_financial_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$adminFinancialServiceHash() =>
    r'9cca5f45d19e9d091a28ea3dc3cbbba72d62827b';

/// ================================================
/// ADMIN FINANCIAL SERVICE (SIMPLE)
/// ================================================
///
/// Copied from [adminFinancialService].
@ProviderFor(adminFinancialService)
final adminFinancialServiceProvider =
    AutoDisposeProvider<AdminFinancialService>.internal(
      adminFinancialService,
      name: r'adminFinancialServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$adminFinancialServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AdminFinancialServiceRef =
    AutoDisposeProviderRef<AdminFinancialService>;
String _$adminDashboardHash() => r'3f1868f8c20e3fc221f83684a4708d4346ad6607';

/// ================================================
/// PROVIDERS (SIMPLE)
/// ================================================
///
/// Copied from [adminDashboard].
@ProviderFor(adminDashboard)
final adminDashboardProvider =
    AutoDisposeFutureProvider<AdminDashboard>.internal(
      adminDashboard,
      name: r'adminDashboardProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$adminDashboardHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AdminDashboardRef = AutoDisposeFutureProviderRef<AdminDashboard>;
String _$financialSummaryHash() => r'badc4905ec212a96b2543bb05fc9c6c08fc85d6a';

/// See also [financialSummary].
@ProviderFor(financialSummary)
final financialSummaryProvider =
    AutoDisposeFutureProvider<FinancialSummary>.internal(
      financialSummary,
      name: r'financialSummaryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$financialSummaryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FinancialSummaryRef = AutoDisposeFutureProviderRef<FinancialSummary>;
String _$walletOverviewHash() => r'12f7447feb2c85ea95a8a8ad8eb5f1983a4c0501';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [walletOverview].
@ProviderFor(walletOverview)
const walletOverviewProvider = WalletOverviewFamily();

/// See also [walletOverview].
class WalletOverviewFamily extends Family<AsyncValue<WalletOverview>> {
  /// See also [walletOverview].
  const WalletOverviewFamily();

  /// See also [walletOverview].
  WalletOverviewProvider call(String walletId) {
    return WalletOverviewProvider(walletId);
  }

  @override
  WalletOverviewProvider getProviderOverride(
    covariant WalletOverviewProvider provider,
  ) {
    return call(provider.walletId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'walletOverviewProvider';
}

/// See also [walletOverview].
class WalletOverviewProvider extends AutoDisposeFutureProvider<WalletOverview> {
  /// See also [walletOverview].
  WalletOverviewProvider(String walletId)
    : this._internal(
        (ref) => walletOverview(ref as WalletOverviewRef, walletId),
        from: walletOverviewProvider,
        name: r'walletOverviewProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$walletOverviewHash,
        dependencies: WalletOverviewFamily._dependencies,
        allTransitiveDependencies:
            WalletOverviewFamily._allTransitiveDependencies,
        walletId: walletId,
      );

  WalletOverviewProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.walletId,
  }) : super.internal();

  final String walletId;

  @override
  Override overrideWith(
    FutureOr<WalletOverview> Function(WalletOverviewRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: WalletOverviewProvider._internal(
        (ref) => create(ref as WalletOverviewRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        walletId: walletId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<WalletOverview> createElement() {
    return _WalletOverviewProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is WalletOverviewProvider && other.walletId == walletId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, walletId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin WalletOverviewRef on AutoDisposeFutureProviderRef<WalletOverview> {
  /// The parameter `walletId` of this provider.
  String get walletId;
}

class _WalletOverviewProviderElement
    extends AutoDisposeFutureProviderElement<WalletOverview>
    with WalletOverviewRef {
  _WalletOverviewProviderElement(super.provider);

  @override
  String get walletId => (origin as WalletOverviewProvider).walletId;
}

String _$auditLogsHash() => r'c87e7ca9d4c5d1d615d937d3d316b89d0ed2271e';

/// See also [auditLogs].
@ProviderFor(auditLogs)
const auditLogsProvider = AuditLogsFamily();

/// See also [auditLogs].
class AuditLogsFamily extends Family<AsyncValue<List<AdminActionLog>>> {
  /// See also [auditLogs].
  const AuditLogsFamily();

  /// See also [auditLogs].
  AuditLogsProvider call({
    String? adminUserId,
    AdminActionType? actionType,
    DateTime? startDate,
    DateTime? endDate,
    int? page,
    int? limit,
  }) {
    return AuditLogsProvider(
      adminUserId: adminUserId,
      actionType: actionType,
      startDate: startDate,
      endDate: endDate,
      page: page,
      limit: limit,
    );
  }

  @override
  AuditLogsProvider getProviderOverride(covariant AuditLogsProvider provider) {
    return call(
      adminUserId: provider.adminUserId,
      actionType: provider.actionType,
      startDate: provider.startDate,
      endDate: provider.endDate,
      page: provider.page,
      limit: provider.limit,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'auditLogsProvider';
}

/// See also [auditLogs].
class AuditLogsProvider
    extends AutoDisposeFutureProvider<List<AdminActionLog>> {
  /// See also [auditLogs].
  AuditLogsProvider({
    String? adminUserId,
    AdminActionType? actionType,
    DateTime? startDate,
    DateTime? endDate,
    int? page,
    int? limit,
  }) : this._internal(
         (ref) => auditLogs(
           ref as AuditLogsRef,
           adminUserId: adminUserId,
           actionType: actionType,
           startDate: startDate,
           endDate: endDate,
           page: page,
           limit: limit,
         ),
         from: auditLogsProvider,
         name: r'auditLogsProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$auditLogsHash,
         dependencies: AuditLogsFamily._dependencies,
         allTransitiveDependencies: AuditLogsFamily._allTransitiveDependencies,
         adminUserId: adminUserId,
         actionType: actionType,
         startDate: startDate,
         endDate: endDate,
         page: page,
         limit: limit,
       );

  AuditLogsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.adminUserId,
    required this.actionType,
    required this.startDate,
    required this.endDate,
    required this.page,
    required this.limit,
  }) : super.internal();

  final String? adminUserId;
  final AdminActionType? actionType;
  final DateTime? startDate;
  final DateTime? endDate;
  final int? page;
  final int? limit;

  @override
  Override overrideWith(
    FutureOr<List<AdminActionLog>> Function(AuditLogsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: AuditLogsProvider._internal(
        (ref) => create(ref as AuditLogsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        adminUserId: adminUserId,
        actionType: actionType,
        startDate: startDate,
        endDate: endDate,
        page: page,
        limit: limit,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<AdminActionLog>> createElement() {
    return _AuditLogsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AuditLogsProvider &&
        other.adminUserId == adminUserId &&
        other.actionType == actionType &&
        other.startDate == startDate &&
        other.endDate == endDate &&
        other.page == page &&
        other.limit == limit;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, adminUserId.hashCode);
    hash = _SystemHash.combine(hash, actionType.hashCode);
    hash = _SystemHash.combine(hash, startDate.hashCode);
    hash = _SystemHash.combine(hash, endDate.hashCode);
    hash = _SystemHash.combine(hash, page.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AuditLogsRef on AutoDisposeFutureProviderRef<List<AdminActionLog>> {
  /// The parameter `adminUserId` of this provider.
  String? get adminUserId;

  /// The parameter `actionType` of this provider.
  AdminActionType? get actionType;

  /// The parameter `startDate` of this provider.
  DateTime? get startDate;

  /// The parameter `endDate` of this provider.
  DateTime? get endDate;

  /// The parameter `page` of this provider.
  int? get page;

  /// The parameter `limit` of this provider.
  int? get limit;
}

class _AuditLogsProviderElement
    extends AutoDisposeFutureProviderElement<List<AdminActionLog>>
    with AuditLogsRef {
  _AuditLogsProviderElement(super.provider);

  @override
  String? get adminUserId => (origin as AuditLogsProvider).adminUserId;
  @override
  AdminActionType? get actionType => (origin as AuditLogsProvider).actionType;
  @override
  DateTime? get startDate => (origin as AuditLogsProvider).startDate;
  @override
  DateTime? get endDate => (origin as AuditLogsProvider).endDate;
  @override
  int? get page => (origin as AuditLogsProvider).page;
  @override
  int? get limit => (origin as AuditLogsProvider).limit;
}

String _$adminReportsHash() => r'1939e5c7db01ae448304b4c085ec46a9f5b920d6';

/// See also [adminReports].
@ProviderFor(adminReports)
const adminReportsProvider = AdminReportsFamily();

/// See also [adminReports].
class AdminReportsFamily extends Family<AsyncValue<List<AdminReport>>> {
  /// See also [adminReports].
  const AdminReportsFamily();

  /// See also [adminReports].
  AdminReportsProvider call({
    ReportType? reportType,
    DateTime? startDate,
    DateTime? endDate,
    int? page,
    int? limit,
  }) {
    return AdminReportsProvider(
      reportType: reportType,
      startDate: startDate,
      endDate: endDate,
      page: page,
      limit: limit,
    );
  }

  @override
  AdminReportsProvider getProviderOverride(
    covariant AdminReportsProvider provider,
  ) {
    return call(
      reportType: provider.reportType,
      startDate: provider.startDate,
      endDate: provider.endDate,
      page: provider.page,
      limit: provider.limit,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'adminReportsProvider';
}

/// See also [adminReports].
class AdminReportsProvider
    extends AutoDisposeFutureProvider<List<AdminReport>> {
  /// See also [adminReports].
  AdminReportsProvider({
    ReportType? reportType,
    DateTime? startDate,
    DateTime? endDate,
    int? page,
    int? limit,
  }) : this._internal(
         (ref) => adminReports(
           ref as AdminReportsRef,
           reportType: reportType,
           startDate: startDate,
           endDate: endDate,
           page: page,
           limit: limit,
         ),
         from: adminReportsProvider,
         name: r'adminReportsProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$adminReportsHash,
         dependencies: AdminReportsFamily._dependencies,
         allTransitiveDependencies:
             AdminReportsFamily._allTransitiveDependencies,
         reportType: reportType,
         startDate: startDate,
         endDate: endDate,
         page: page,
         limit: limit,
       );

  AdminReportsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.reportType,
    required this.startDate,
    required this.endDate,
    required this.page,
    required this.limit,
  }) : super.internal();

  final ReportType? reportType;
  final DateTime? startDate;
  final DateTime? endDate;
  final int? page;
  final int? limit;

  @override
  Override overrideWith(
    FutureOr<List<AdminReport>> Function(AdminReportsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: AdminReportsProvider._internal(
        (ref) => create(ref as AdminReportsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        reportType: reportType,
        startDate: startDate,
        endDate: endDate,
        page: page,
        limit: limit,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<AdminReport>> createElement() {
    return _AdminReportsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AdminReportsProvider &&
        other.reportType == reportType &&
        other.startDate == startDate &&
        other.endDate == endDate &&
        other.page == page &&
        other.limit == limit;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, reportType.hashCode);
    hash = _SystemHash.combine(hash, startDate.hashCode);
    hash = _SystemHash.combine(hash, endDate.hashCode);
    hash = _SystemHash.combine(hash, page.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AdminReportsRef on AutoDisposeFutureProviderRef<List<AdminReport>> {
  /// The parameter `reportType` of this provider.
  ReportType? get reportType;

  /// The parameter `startDate` of this provider.
  DateTime? get startDate;

  /// The parameter `endDate` of this provider.
  DateTime? get endDate;

  /// The parameter `page` of this provider.
  int? get page;

  /// The parameter `limit` of this provider.
  int? get limit;
}

class _AdminReportsProviderElement
    extends AutoDisposeFutureProviderElement<List<AdminReport>>
    with AdminReportsRef {
  _AdminReportsProviderElement(super.provider);

  @override
  ReportType? get reportType => (origin as AdminReportsProvider).reportType;
  @override
  DateTime? get startDate => (origin as AdminReportsProvider).startDate;
  @override
  DateTime? get endDate => (origin as AdminReportsProvider).endDate;
  @override
  int? get page => (origin as AdminReportsProvider).page;
  @override
  int? get limit => (origin as AdminReportsProvider).limit;
}

String _$adminWalletsHash() => r'62d79ab532c56679ed97e0bdcf96cc2345666113';

/// See also [adminWallets].
@ProviderFor(adminWallets)
const adminWalletsProvider = AdminWalletsFamily();

/// See also [adminWallets].
class AdminWalletsFamily extends Family<AsyncValue<List<WalletListItem>>> {
  /// See also [adminWallets].
  const AdminWalletsFamily();

  /// See also [adminWallets].
  AdminWalletsProvider call(String searchTerm) {
    return AdminWalletsProvider(searchTerm);
  }

  @override
  AdminWalletsProvider getProviderOverride(
    covariant AdminWalletsProvider provider,
  ) {
    return call(provider.searchTerm);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'adminWalletsProvider';
}

/// See also [adminWallets].
class AdminWalletsProvider
    extends AutoDisposeFutureProvider<List<WalletListItem>> {
  /// See also [adminWallets].
  AdminWalletsProvider(String searchTerm)
    : this._internal(
        (ref) => adminWallets(ref as AdminWalletsRef, searchTerm),
        from: adminWalletsProvider,
        name: r'adminWalletsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$adminWalletsHash,
        dependencies: AdminWalletsFamily._dependencies,
        allTransitiveDependencies:
            AdminWalletsFamily._allTransitiveDependencies,
        searchTerm: searchTerm,
      );

  AdminWalletsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.searchTerm,
  }) : super.internal();

  final String searchTerm;

  @override
  Override overrideWith(
    FutureOr<List<WalletListItem>> Function(AdminWalletsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: AdminWalletsProvider._internal(
        (ref) => create(ref as AdminWalletsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        searchTerm: searchTerm,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<WalletListItem>> createElement() {
    return _AdminWalletsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AdminWalletsProvider && other.searchTerm == searchTerm;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, searchTerm.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AdminWalletsRef on AutoDisposeFutureProviderRef<List<WalletListItem>> {
  /// The parameter `searchTerm` of this provider.
  String get searchTerm;
}

class _AdminWalletsProviderElement
    extends AutoDisposeFutureProviderElement<List<WalletListItem>>
    with AdminWalletsRef {
  _AdminWalletsProviderElement(super.provider);

  @override
  String get searchTerm => (origin as AdminWalletsProvider).searchTerm;
}

String _$financialAlertsHash() => r'acd3d1c64e91c4d16958854d65e16b94f52b64da';

/// ================================================
/// SIMPLE PROVIDERS FOR ALERTS AND LOGS
/// ================================================
///
/// Copied from [financialAlerts].
@ProviderFor(financialAlerts)
final financialAlertsProvider =
    AutoDisposeFutureProvider<List<FinancialAlert>>.internal(
      financialAlerts,
      name: r'financialAlertsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$financialAlertsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FinancialAlertsRef = AutoDisposeFutureProviderRef<List<FinancialAlert>>;
String _$adminActionLogsHash() => r'260827acf2f382c32d8bbe3dab21a41fb07d1eb1';

/// See also [adminActionLogs].
@ProviderFor(adminActionLogs)
final adminActionLogsProvider =
    AutoDisposeFutureProvider<List<AdminActionLog>>.internal(
      adminActionLogs,
      name: r'adminActionLogsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$adminActionLogsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AdminActionLogsRef = AutoDisposeFutureProviderRef<List<AdminActionLog>>;
String _$walletAdjustmentNotifierHash() =>
    r'e77e04dbeb8035d5a37484d6a9cc38ceda8e70a5';

/// See also [WalletAdjustmentNotifier].
@ProviderFor(WalletAdjustmentNotifier)
final walletAdjustmentNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      WalletAdjustmentNotifier,
      WalletAdjustmentResponse?
    >.internal(
      WalletAdjustmentNotifier.new,
      name: r'walletAdjustmentNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$walletAdjustmentNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$WalletAdjustmentNotifier =
    AutoDisposeAsyncNotifier<WalletAdjustmentResponse?>;
String _$alertsNotifierHash() => r'9f8b3e246926e0e17fd4d4343ee7811a3c88e0b1';

/// See also [AlertsNotifier].
@ProviderFor(AlertsNotifier)
final alertsNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      AlertsNotifier,
      List<FinancialAlert>
    >.internal(
      AlertsNotifier.new,
      name: r'alertsNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$alertsNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AlertsNotifier = AutoDisposeAsyncNotifier<List<FinancialAlert>>;
String _$transactionAnalysisNotifierHash() =>
    r'fe9cce85ab3c292168e962ef4eec207effa91a70';

/// See also [TransactionAnalysisNotifier].
@ProviderFor(TransactionAnalysisNotifier)
final transactionAnalysisNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      TransactionAnalysisNotifier,
      TransactionAnalysis?
    >.internal(
      TransactionAnalysisNotifier.new,
      name: r'transactionAnalysisNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$transactionAnalysisNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$TransactionAnalysisNotifier =
    AutoDisposeAsyncNotifier<TransactionAnalysis?>;
String _$reportGeneratorNotifierHash() =>
    r'21b839ec327851a85c1823b3910832d4548dbd5e';

/// See also [ReportGeneratorNotifier].
@ProviderFor(ReportGeneratorNotifier)
final reportGeneratorNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      ReportGeneratorNotifier,
      AdminReport?
    >.internal(
      ReportGeneratorNotifier.new,
      name: r'reportGeneratorNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$reportGeneratorNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$ReportGeneratorNotifier = AutoDisposeAsyncNotifier<AdminReport?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
