/// --------------------------------
/// شاشة موحدة للملف الشخصي تدمج وظائف عرض وتعديل وإكمال المعلومات الشخصية
/// في مكون واحد قابل للتكوين.
///
/// تدعم هذه الشاشة عدة أوضاع:
library;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_vector_icons/flutter_vector_icons.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/auth/auth_providers.dart';
import '../../../core/auth/simple_auth_system.dart';
import '../../../core/providers/users_provider.dart';
import '../../../core/navigation/unified_app_bar.dart';
import '../../../core/widgets/unified_card.dart';
import '../../../core/utils/unified_theme_extension.dart';
import '../../../core/theme/app_theme.dart';
import '../../../l10n/app_localizations.dart';
import '../models/user_model.dart';

import '../widgets/account_list_tile.dart';
import '../widgets/section_header.dart';

/// أوضاع عرض الملف الشخصي
/// [view] - وضع العرض فقط (بدون تحرير)
/// [edit] - وضع التحرير (يسمح بتغيير المعلومات وحفظها)
enum ProfileMode { view, edit }

/// شاشة الملف الشخصي الموحدة
/// تستخدم لعرض وتحرير وإكمال معلومات الملف الشخصي للمستخدم
class UnifiedProfileScreen extends ConsumerWidget {
  /// إنشاء شاشة الملف الشخصي الموحدة
  const UnifiedProfileScreen({
    super.key,
    this.mode = ProfileMode.view,
    this.canGoBack = true,
    this.redirectAfterSave,
  });

  /// وضع عرض الشاشة (عرض أو تحرير)، الافتراضي هو العرض فقط
  final ProfileMode mode;

  /// إذا كان يمكن للمستخدم العودة (يُستخدم لإكمال الملف الشخصي)
  final bool canGoBack;

  /// مسار إعادة التوجيه الاختياري بعد الحفظ
  final String? redirectAfterSave;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    // Fixed: Use currentUserStreamProvider instead of deprecated currentUserStreamProvider
    final currentUserAsync = ref.watch(currentUserStreamProvider);

    return currentUserAsync.when(
      data: (currentUser) {
        // Debug logging لفهم حالة المستخدم
        debugPrint(
          '🔍 UnifiedProfileScreen: currentUser state: ${currentUser.runtimeType}',
        );
        debugPrint(
          '🔍 UnifiedProfileScreen: User data: name=${currentUser?.name}, email=${currentUser?.email}, id=${currentUser?.id}',
        );

        // This screen is now only for viewing the account summary.
        // Editing is handled by a dedicated screen pushed on top.

        debugPrint(
          '🔍 UnifiedProfileScreen: Building with user: ${currentUser != null ? 'authenticated' : 'guest'}',
        );
        return _buildProfileScreen(context, ref, currentUser, l10n);
      },
      loading: () => const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      ),
      error: (error, stack) {
        debugPrint('🔍 UnifiedProfileScreen: Error loading user: $error');
        return _buildProfileScreen(context, ref, null, l10n);
      },
    );
  }

  Widget _buildProfileScreen(
    BuildContext context,
    WidgetRef ref,
    UserModel? currentUser,
    AppLocalizations l10n,
  ) {
    // إذا كان المستخدم غير مسجل (زائر)
    debugPrint(
      '🔍 _buildProfileScreen: currentUser is ${currentUser == null ? 'null (guest)' : 'not null (authenticated)'}',
    );

    if (currentUser == null) {
      debugPrint('🔍 _buildProfileScreen: Showing guest screen');
      return _buildGuestScreen(context, l10n);
    }

    debugPrint('🔍 _buildProfileScreen: Showing authenticated user screen');
    debugPrint(
      '🔍 _buildProfileScreen: User details - name: ${currentUser.name}, email: ${currentUser.email}',
    );

    // --- Seller Status Logic ---
    final sellerStatus = currentUser.sellerStatus;

    // Build the list of widgets dynamically
    final List<Widget> listItems = _buildAccountListItems(
      context,
      ref,
      l10n,
      currentUser,
      sellerStatus,
    );

    return Scaffold(
      appBar: const UnifiedAppBar(title: 'الحساب', showNotifications: false),
      body: ListView.separated(
        padding: EdgeInsets.symmetric(
          horizontal: context.paddingMedium,
        ).copyWith(bottom: context.paddingLarge),
        itemCount: listItems.length,
        itemBuilder: (context, index) => listItems[index],
        separatorBuilder: (context, index) {
          // Add a Divider between specific sections if needed,
          // for now, we can rely on SizedBoxes within the list.
          // This provides more granular control than a simple ListView.
          // Example: return const Divider();
          // We can return an empty container if no separator is needed.
          return const SizedBox.shrink();
        },
      ),
    );
  }

  /// Builds the dynamic list of widgets for the account screen.
  /// This approach is cleaner and prepares for using ListView.builder.
  List<Widget> _buildAccountListItems(
    BuildContext context,
    WidgetRef ref,
    AppLocalizations l10n,
    UserModel currentUser,
    SellerStatus sellerStatus,
  ) {
    final List<Widget> items = [
      SizedBox(height: context.paddingSmall),
      // User Info Header
      _UserInfoHeader(user: currentUser),
      SizedBox(height: context.paddingLarge),
    ];

    // --- DEBUG BOX ---
    if (kDebugMode) {
      items.add(
        Container(
          margin: EdgeInsets.only(bottom: context.paddingLarge),
          child: UnifiedCard(
            borderColor: Colors.red,
            backgroundColor: Colors.red.withValues(alpha: 0.1),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '🔍 DEBUG - Seller Info',
                  style: TextStyle(
                    color: Colors.red[800],
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const Divider(height: 16),
                Text('Role: ${currentUser.role}'),
                Text('Is Approved: ${currentUser.isApproved}'),
                Text('Is Seller Requested: ${currentUser.isSellerRequested}'),
                Text(
                  'Final Status: ${sellerStatus.name}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // --- Personal Activity Section ---
    items.addAll([
      const SectionHeader(title: 'نشاطي الشخصي'),
      AccountListTile(
        title: 'المفضلة',
        subtitle: 'عرض ومراقبة المنتجات المفضلة لديك',
        icon: Ionicons.heart_outline,
        onTap: () {
          debugPrint('Navigating to /favorites');
          context.push('/favorites');
        },
      ),
      AccountListTile(
        title: 'محفظتي',
        subtitle: 'عرض وإدارة محفظتك',
        icon: Icons.account_balance_wallet_outlined,
        onTap: () {
          debugPrint('Navigating to /wallet');
          context.push('/wallet');
        },
      ),
      AccountListTile(
        title: l10n.myOrders, // My purchase orders
        subtitle: 'تتبع مشترياتك وحالتها',
        icon: Ionicons.briefcase_outline,
        onTap: () {
          debugPrint('Navigating to /orders');
          context.push('/orders');
        },
      ),
      AccountListTile(
        title: 'مرآبي',
        subtitle: 'عرض وإدارة مركباتك',
        icon: Ionicons.car_sport_outline,
        onTap: () {
          debugPrint('Navigating to /garage');
          context.push('/garage');
        },
      ),
      AccountListTile(
        title: l10n.messages, // Unified messages
        subtitle: l10n.viewBuyerSellerMessages,
        icon: Ionicons.chatbubbles_outline,
        onTap: () {
          debugPrint('Navigating to /chat');
          context.push('/chat');
        },
      ),
      const Divider(height: AppTheme.spacingXL),
    ]);

    // --- Seller Management Section ---
    if (sellerStatus == SellerStatus.approved) {
      items.addAll([
        const SectionHeader(title: 'إدارة المتجر'),
        AccountListTile(
          title: 'لوحة تحكم البائع',
          subtitle: 'عرض تحليلات وإحصائيات المبيعات',
          icon: Ionicons.speedometer_outline,
          onTap: () => context.push('/seller/dashboard'),
        ),
        AccountListTile(
          title: 'منتجاتي (إعلاناتي)',
          subtitle: 'إدارة المنتجات المعروضة للبيع',
          icon: Ionicons.list_outline,
          onTap: () => context.push('/seller/products'),
        ),
        AccountListTile(
          title: 'طلبات العملاء',
          subtitle: 'متابعة الطلبات الجديدة والحالية',
          icon: Ionicons.receipt_outline,
          onTap: () => context.push('/seller/orders'),
        ),
        const Divider(height: AppTheme.spacingXL),
      ]);
    }

    // --- Seller Application Section (for non-sellers) ---
    if (sellerStatus != SellerStatus.approved) {
      if (sellerStatus == SellerStatus.requestPending) {
        items.add(const _RequestPendingCard());
      } else if (currentUser.canRequestTobeSeller) {
        items.addAll([
          SectionHeader(title: l10n.sellerSection),
          AccountListTile(
            title: l10n.becomeASeller,
            subtitle: l10n.applyToSellOnCarNow,
            icon: Ionicons.cash_outline,
            onTap: () {
              debugPrint('Navigating to /seller/subscription-request');
              context.push('/seller/subscription-request');
            },
          ),
        ]);
      }
      items.add(const Divider(height: AppTheme.spacingXL));
    }

    // Settings and Support Section (Common for all users)
    items.addAll([
      const Divider(height: AppTheme.spacingXL),
      SectionHeader(title: l10n.settingsAndSupport),
      AccountListTile(
        title: l10n.personalInformation,
        subtitle: l10n.editYourProfileDetails,
        icon: Ionicons.person_circle_outline,
        onTap: () {
          debugPrint('Navigating to /account/profile/edit');
          context.push('/account/profile/edit');
        },
      ),
      AccountListTile(
        title: l10n.settings,
        subtitle: l10n.accountSettings,
        icon: Ionicons.settings_outline,
        onTap: () {
          debugPrint('Navigating to /settings');
          context.push('/settings');
        },
      ),
      AccountListTile(
        title: l10n.support,
        subtitle: l10n.getHelpAndContactSupport,
        icon: Ionicons.help_buoy_outline,
        onTap: () {
          debugPrint('Navigating to /support');
          context.push('/support');
        },
      ),
      AccountListTile(
        title: l10n.logout,
        subtitle: l10n.signOutFromYourAccount,
        icon: Ionicons.log_out_outline,
        isDestructive: true,
        onTap: () async {
          debugPrint('Signing out...');
          try {
            final authSystem = ref.read(simpleAuthSystemProvider.notifier);
        await authSystem.signOut();
            debugPrint('Sign out successful');
            // Navigation is now handled globally by the auth system
            // No need for manual navigation here
          } catch (e) {
            debugPrint('Sign out error: $e');
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('خطأ في تسجيل الخروج: $e'),
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
              );
            }
          }
        },
      ),
    ]);

    return items;
  }

  /// شاشة المستخدم الزائر (غير مسجل)
  Widget _buildGuestScreen(BuildContext context, AppLocalizations l10n) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.account),
        centerTitle: true,
        elevation: 0,
        backgroundColor: theme.scaffoldBackgroundColor,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingM),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Icon(
                Ionicons.person_circle_outline,
                size: 80,
                color: colorScheme.secondary,
              ),
              const SizedBox(height: AppTheme.spacingM),
              Text(
                l10n.welcomeGuest,
                style: theme.textTheme.headlineSmall,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppTheme.spacingS),
              Text(
                l10n.guestSubtitle,
                style: theme.textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppTheme.spacingM),
              FilledButton(
                style: FilledButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  foregroundColor: colorScheme.onPrimary,
                  padding: const EdgeInsets.symmetric(
                    vertical: AppTheme.spacingS,
                  ),
                ),
                onPressed: () {
                  debugPrint('Navigating to /login');
                  context.push('/login');
                },
                child: Text(l10n.loginOrRegister),
              ),
              const SizedBox(height: AppTheme.spacingL),

              // Even as a guest, some options might be available
              SectionHeader(title: l10n.settingsAndSupport),
              const AccountListTile(
                title: 'الإعدادات',
                subtitle: 'إعدادات التطبيق',
                icon: Ionicons.settings_outline,
                // onTap is null, so it will be disabled
              ),
              AccountListTile(
                title: l10n.support,
                subtitle: l10n.getHelpAndContactSupport,
                icon: Ionicons.help_buoy_outline,
                onTap: () {
                  debugPrint('Navigating to /support');
                  context.push('/support');
                },
              ),
              const SizedBox(height: AppTheme.spacingXL),
              const Center(
                child: Text('Version 1.0'), // Example version
              ),
              const SizedBox(height: AppTheme.spacingL),
            ],
          ),
        ),
      ),
    );
  }
}

/// A card to show when a seller application is pending.
class _RequestPendingCard extends StatelessWidget {
  const _RequestPendingCard();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      margin: const EdgeInsets.symmetric(vertical: AppTheme.spacingS),
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: theme.colorScheme.secondaryContainer.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.colorScheme.secondaryContainer),
      ),
      child: Row(
        children: [
          Icon(
            Ionicons.hourglass_outline,
            color: theme.colorScheme.onSecondaryContainer,
          ),
          const SizedBox(width: AppTheme.spacingM),
          Expanded(
            child: Text(
              'طلبك لتصبح بائعاً قيد المراجعة حالياً.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSecondaryContainer,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Header widget displaying user's avatar, name, and email.
class _UserInfoHeader extends StatelessWidget {
  const _UserInfoHeader({required this.user});

  final UserModel user;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return InkWell(
      onTap: () {
        context.push('/account/profile/edit');
      },
      borderRadius: BorderRadius.circular(AppTheme.radiusM),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        child: Row(
          children: [
            CircleAvatar(
              radius: 36,
              backgroundColor: colorScheme.surfaceContainerHighest,
              backgroundImage:
                  (user.profileImageUrl != null &&
                      user.profileImageUrl!.isNotEmpty)
                  ? NetworkImage(user.profileImageUrl!)
                  : null,
              child:
                  (user.profileImageUrl == null ||
                      user.profileImageUrl!.isEmpty)
                  ? Icon(
                      Ionicons.person_outline,
                      size: 36,
                      color: colorScheme.onSurfaceVariant,
                    )
                  : null,
            ),
            const SizedBox(width: AppTheme.spacingM),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user.name ?? 'عضو جديد',
                    style: theme.textTheme.titleLarge,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    user.email ?? 'بدون بريد إلكتروني',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  // عرض المدينة إذا كانت متوفرة
                  if (user.cityName != null && user.cityName!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 2),
                      child: Row(
                        children: [
                          Icon(
                            Icons.location_on,
                            size: 14,
                            color: colorScheme.onSurfaceVariant,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              user.cityName!,
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: colorScheme.onSurfaceVariant,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: colorScheme.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }
}
