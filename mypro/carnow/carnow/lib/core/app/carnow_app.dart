import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dynamic_color/dynamic_color.dart';
import '../theme/carnow_colors.dart';
import 'package:go_router/go_router.dart';

import 'package:carnow/core/theme/app_theme.dart';
import 'package:carnow/core/router/app_router.dart';
import 'package:carnow/core/app/performance_main_thread_fix.dart';
import 'package:carnow/core/utils/unified_logger.dart';
import 'package:carnow/l10n/app_localizations.dart';

/// CarNow Application - Main App Widget
/// 
/// This is the root widget of the CarNow application that sets up:
/// - Theme and localization
/// - Navigation and routing
/// - Performance optimizations
/// - Error handling
class CarNowApp extends ConsumerStatefulWidget {
  const CarNowApp({super.key});

  @override
  ConsumerState<CarNowApp> createState() => _CarNowAppState();
}

class _CarNowAppState extends ConsumerState<CarNowApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    
    // Apply performance fixes
    PerformanceMainThreadFix.applyAllFixes();
    
    UnifiedLogger.info('🚀 CarNowApp initialized with performance optimizations');
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    switch (state) {
      case AppLifecycleState.resumed:
        UnifiedLogger.info('📱 App resumed');
        break;
      case AppLifecycleState.inactive:
        UnifiedLogger.info('📱 App inactive');
        break;
      case AppLifecycleState.paused:
        UnifiedLogger.info('📱 App paused');
        break;
      case AppLifecycleState.detached:
        UnifiedLogger.info('📱 App detached');
        break;
      case AppLifecycleState.hidden:
        UnifiedLogger.info('📱 App hidden');
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch router for navigation changes with error handling
    GoRouter router;
    try {
      router = ref.watch(appRouterProvider);
    } catch (e) {
      UnifiedLogger.error('Failed to initialize app router: $e');
      // Return a simple error screen if router initialization fails
      return MaterialApp(
        title: 'CarNow',
        debugShowCheckedModeBanner: false,
        home: Scaffold(
          appBar: AppBar(
            title: const Text('CarNow'),
          ),
          body: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: CarnowColors.error),
                SizedBox(height: 16),
                Text(
                  'خطأ في تهيئة التطبيق',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 8),
                Text(
                  'يرجى إعادة تشغيل التطبيق',
                  style: TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        ),
      );
    }
    
    return DynamicColorBuilder(
      builder: (ColorScheme? lightDynamic, ColorScheme? darkDynamic) {
        return MaterialApp.router(
          title: 'CarNow',
          debugShowCheckedModeBanner: false,
          
          // Theme configuration
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.system,
          
          // Localization
          localizationsDelegates: AppLocalizations.localizationsDelegates,
          supportedLocales: AppLocalizations.supportedLocales,
          locale: const Locale('ar'),
          
          // Router configuration
          routerConfig: router,
          
          // Performance optimizations
          builder: (context, child) {
            // Apply performance optimizations to the widget tree
            return MediaQuery(
              data: MediaQuery.of(context).copyWith(
                textScaler: const TextScaler.linear(1.0),
              ),
              child: child!,
            );
          },
          
          // Error handling
          onGenerateTitle: (context) => 'CarNow',
        );
      },
    );
  }
}
