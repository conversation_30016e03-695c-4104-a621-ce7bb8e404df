import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'server_urls.dart';

/// Simple and fast backend configuration
/// Checks local server once at startup, then uses cached result
class BackendConfig {
  // ==================== CONFIGURATION (Easy to change) ====================
  
  /// Health check timeout for server discovery (very fast)
  static const Duration _healthCheckTimeout = Duration(milliseconds: 500);
  
  /// Cache the discovered server URL (set once at startup)
  static String? _cachedServerUrl;
  static bool _hasCheckedLocal = false;

  // ==================== FAST SERVER DISCOVERY ====================
  
  /// Get the best available backend URL (fast, cached)
  static String get baseUrl {
    // Return cached result if available
    if (_cachedServerUrl != null) {
      return _cachedServerUrl!;
    }
    
    // First time: check local server quickly
    if (!_hasCheckedLocal && kDebugMode) {
      _checkLocalServerOnce();
    }
    
    // Return hosted server as fallback
    return ServerUrls.productionBackend;
  }
  
  /// Check local server once at startup (non-blocking)
  static void _checkLocalServerOnce() {
    _hasCheckedLocal = true;
    
    // Quick check in background
    _tryLocalServer().then((localUrl) {
      if (localUrl != null) {
        _cachedServerUrl = localUrl;
        debugPrint('🔍 Using local server: $localUrl');
      } else {
        _cachedServerUrl = ServerUrls.productionBackend;
        debugPrint('🔍 Using hosted server: ${ServerUrls.productionBackend}');
      }
    }).catchError((e) {
      _cachedServerUrl = ServerUrls.productionBackend;
      debugPrint('🔍 Using hosted server (error): ${ServerUrls.productionBackend}');
    });
  }
  
  /// Try to connect to local server (fast timeout)
  static Future<String?> _tryLocalServer() async {
    final localUrls = [
      ServerUrls.localBackend,
      ServerUrls.androidEmulatorBackend,
    ];
    
    for (final url in localUrls) {
      try {
        final isHealthy = await _checkServerHealth(url);
        if (isHealthy) {
          return url;
        }
      } catch (e) {
        // Continue to next URL
      }
    }
    
    return null;
  }
  
  /// Check if server is healthy (very fast)
  static Future<bool> _checkServerHealth(String baseUrl) async {
    try {
      final healthUrl = ServerUrls.buildHealthUrl(baseUrl);
      final response = await http.get(
        Uri.parse(healthUrl),
        headers: {'Accept': 'application/json'},
      ).timeout(_healthCheckTimeout);
      
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
  
  /// Clear server cache (useful for testing)
  static void clearCache() {
    _cachedServerUrl = null;
    _hasCheckedLocal = false;
  }

  // ==================== URL BUILDERS ====================
  
  /// Build full API URL
  static String buildApiUrl(String endpoint) {
    final base = baseUrl;
    return ServerUrls.buildApiUrl(base, endpoint);
  }
  
  /// Get health check URL
  static String get healthUrl {
    final base = baseUrl;
    return ServerUrls.buildHealthUrl(base);
  }
  
  /// Get ready check URL
  static String get readyUrl {
    final base = baseUrl;
    return ServerUrls.buildReadyUrl(base);
  }

  // ==================== LEGACY SUPPORT ====================
  
  /// Legacy getter for backward compatibility
  static String get baseUrlSync {
    return baseUrl;
  }
  
  /// Legacy URL builder for backward compatibility
  static String buildUrl(String endpoint) {
    return buildApiUrl(endpoint);
  }
  
  /// Request timeout duration for backward compatibility
  static const Duration requestTimeout = Duration(seconds: 30);

  // ==================== API ENDPOINTS ====================
  
  // Seller endpoints
  static const String sellerApplicationsEndpoint = '/seller-applications';
  static const String sellersEndpoint = '/sellers';
  static const String sellerStoreEndpoint = '/sellers/store';
  static const String sellerProfileEndpoint = '/sellers/profile';
  static const String sellerSubscriptionsEndpoint = '/sellers/subscriptions';

  // ==================== CONFIGURATION HELPERS ====================
  
  /// Get current server information
  static Map<String, dynamic> getServerInfo() {
    return {
      'cached_url': _cachedServerUrl,
      'has_checked_local': _hasCheckedLocal,
      'hosted_url': ServerUrls.productionBackend,
      'local_urls': [ServerUrls.localBackend, ServerUrls.androidEmulatorBackend],
      'health_check_timeout_ms': _healthCheckTimeout.inMilliseconds,
    };
  }
  
  /// Force refresh server discovery
  static Future<String> refreshServerDiscovery() async {
    clearCache();
    _checkLocalServerOnce();
    return baseUrl;
  }
  
  /// Print current server configuration
  static void printServerConfiguration() {
    print('🔗 Current Backend Configuration:');
    print('   Cached URL: ${_cachedServerUrl ?? 'None'}');
    print('   Has Checked Local: $_hasCheckedLocal');
    print('   Hosted URL: ${ServerUrls.productionBackend}');
    print('   Local URLs: ${[ServerUrls.localBackend, ServerUrls.androidEmulatorBackend]}');
    ServerUrls.printConfiguration();
  }
}

/// Configuration for CarNow Backend (Go) integration
class CarnowBackendConfig {
  /// Get the current backend base URL
  static String get baseUrl {
    return BackendConfig.baseUrl;
  }
  
  /// Fallback URL for immediate access
  static String get fallbackUrl {
    return BackendConfig.baseUrlSync;
  }
  
  /// Synchronous base URL for backward compatibility
  static String get baseUrlSync {
    return BackendConfig.baseUrlSync;
  }

  /// API version
  static String get apiVersion => ServerUrls.apiVersion;

  /// Full API base URL
  static String get apiBaseUrl {
    final base = baseUrl;
    return '$base${ServerUrls.apiBasePath}';
  }
  
  /// Synchronous API base URL for backward compatibility
  static String get apiBaseUrlSync {
    final base = baseUrlSync;
    return '$base${ServerUrls.apiBasePath}';
  }

  /// Request timeout duration
  static const Duration requestTimeout = Duration(seconds: 30);

  /// API Endpoints
  static const String healthEndpoint = '/health';
  static const String publicHealthEndpoint = '/health';

  // User endpoints
  static const String usersEndpoint = '/users';
  static const String createUserEndpoint = '/users';
  static const String getUserEndpoint = '/users';

  // Wallet endpoints
  static const String walletEndpoint = '/wallets';
  static const String updateWalletEndpoint = '/wallets';
  static const String walletTransactionsEndpoint = '/wallets';
  static const String getWalletTransactionsEndpoint = '/wallets/transactions';

  // Product endpoints
  static const String productsEndpoint = '/products';
  static const String productsSimpleEndpoint = '/products-simple';
  static const String productDetailsEndpoint = '/products';
  static const String searchProductsEndpoint = '/products/search';
  static const String categoriesEndpoint = '/categories';
  static const String brandsEndpoint = '/brands';

  // Order endpoints
  static const String ordersEndpoint = '/orders';
  static const String createOrderEndpoint = '/orders';
  static const String orderHistoryEndpoint = '/orders/history';
  static const String orderStatusEndpoint = '/orders';

  // Cart endpoints
  static const String cartEndpoint = '/cart';
  static const String addToCartEndpoint = '/cart/items';
  static const String removeFromCartEndpoint = '/cart/items';
  static const String updateCartItemEndpoint = '/cart/items';
  static const String clearCartEndpoint = '/cart/clear';

  // Authentication endpoints
  static const String authEndpoint = '/auth';
  static const String loginEndpoint = '/auth/login';
  static const String registerEndpoint = '/auth/register';
  static const String refreshTokenEndpoint = '/auth/refresh';
  static const String logoutEndpoint = '/auth/logout';

  // Profile endpoints
  static const String profileEndpoint = '/profile';
  static const String updateProfileEndpoint = '/profile';
  static const String changePasswordEndpoint = '/profile/password';

  // Notification endpoints
  static const String notificationsEndpoint = '/notifications';
  static const String markNotificationReadEndpoint = '/notifications/read';

  // Search and filter endpoints
  static const String searchEndpoint = '/search';
  static const String filtersEndpoint = '/filters';
  static const String suggestionsEndpoint = '/suggestions';

  // Admin endpoints
  static const String adminEndpoint = '/admin';
  static const String adminUsersEndpoint = '/admin/users';
  static const String adminOrdersEndpoint = '/admin/orders';
  static const String adminProductsEndpoint = '/admin/products';
  static const String adminAnalyticsEndpoint = '/admin/analytics';

  // Seller endpoints
  static const String sellerApplicationsEndpoint = '/seller-applications';
  static const String sellersEndpoint = '/sellers';
  static const String sellerStoreEndpoint = '/sellers/store';
  static const String sellerProfileEndpoint = '/sellers/profile';
  static const String sellerSubscriptionsEndpoint = '/sellers/subscriptions';

  // Utility endpoints
  static const String uploadEndpoint = '/upload';
  static const String downloadEndpoint = '/download';
  static const String exportEndpoint = '/export';
  static const String importEndpoint = '/import';

  /// Build URL for endpoint
  static String buildUrl(String endpoint) {
    final base = baseUrl;
    return ServerUrls.buildApiUrl(base, endpoint);
  }
  
  /// Synchronous build URL for backward compatibility
  static String buildUrlSync(String endpoint) {
    final base = baseUrlSync;
    return ServerUrls.buildApiUrl(base, endpoint);
  }
  
  /// Google OAuth URL for backward compatibility
  static String get googleOAuthUrl => '$apiBaseUrlSync/auth/google';
}

// -----------------------------------------------------------------------------
// إعدادات Supabase
// -----------------------------------------------------------------------------

class SupabaseConfig {
  SupabaseConfig._();

  // Carnow (general) project – used only for authentication when required
  static const String carnowUrl = 'https://lpxtghyvxuenyyisrrro.supabase.co';
  static const String carnowAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxweHRnaHl2eHVlbnl5aXNycnJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI2MjIxNzgsImV4cCI6MjA2ODE5ODE3OH0.7GQrSfJwLQskYOcu9nusXF1amMnezpw3s96sk-zHQ64';
}

// -----------------------------------------------------------------------------
// إعدادات المصادقة والأمان
// -----------------------------------------------------------------------------

class AuthConfig {
  AuthConfig._();

  /// Default currency for Carnow wallet operations.
  static const String defaultCurrency = 'LYD';
}
