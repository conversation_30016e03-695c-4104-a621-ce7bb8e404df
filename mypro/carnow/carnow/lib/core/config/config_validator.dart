import 'dart:io';

import 'environment_config.dart';
import 'secure_config_manager.dart';
import 'feature_flags_manager.dart';

/// Configuration validation result with detailed error reporting
class ConfigValidationResult {
  final bool isValid;
  final List<ConfigValidationError> errors;
  final List<ConfigValidationWarning> warnings;
  final Map<String, dynamic> metadata;

  const ConfigValidationResult({
    required this.isValid,
    this.errors = const [],
    this.warnings = const [],
    this.metadata = const {},
  });

  factory ConfigValidationResult.valid([Map<String, dynamic>? metadata]) {
    return ConfigValidationResult(
      isValid: true,
      metadata: metadata ?? {},
    );
  }

  factory ConfigValidationResult.invalid(
    List<ConfigValidationError> errors, [
    List<ConfigValidationWarning>? warnings,
    Map<String, dynamic>? metadata,
  ]) {
    return ConfigValidationResult(
      isValid: false,
      errors: errors,
      warnings: warnings ?? [],
      metadata: metadata ?? {},
    );
  }

  /// Get summary of validation results
  String get summary {
    if (isValid) {
      return 'Configuration validation passed${warnings.isNotEmpty ? ' with ${warnings.length} warnings' : ''}';
    }
    return 'Configuration validation failed with ${errors.length} errors${warnings.isNotEmpty ? ' and ${warnings.length} warnings' : ''}';
  }

  @override
  String toString() {
    final buffer = StringBuffer();
    buffer.writeln(summary);
    
    if (errors.isNotEmpty) {
      buffer.writeln('\nErrors:');
      for (final error in errors) {
        buffer.writeln('  • ${error.message} (${error.category})');
        if (error.suggestion != null) {
          buffer.writeln('    Suggestion: ${error.suggestion}');
        }
      }
    }
    
    if (warnings.isNotEmpty) {
      buffer.writeln('\nWarnings:');
      for (final warning in warnings) {
        buffer.writeln('  • ${warning.message} (${warning.category})');
        if (warning.suggestion != null) {
          buffer.writeln('    Suggestion: ${warning.suggestion}');
        }
      }
    }
    
    return buffer.toString();
  }
}

/// Configuration validation error
class ConfigValidationError {
  final String message;
  final String category;
  final String? field;
  final String? suggestion;
  final ConfigValidationSeverity severity;

  const ConfigValidationError({
    required this.message,
    required this.category,
    this.field,
    this.suggestion,
    this.severity = ConfigValidationSeverity.error,
  });

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'category': category,
      'field': field,
      'suggestion': suggestion,
      'severity': severity.name,
    };
  }
}

/// Configuration validation warning
class ConfigValidationWarning {
  final String message;
  final String category;
  final String? field;
  final String? suggestion;

  const ConfigValidationWarning({
    required this.message,
    required this.category,
    this.field,
    this.suggestion,
  });

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'category': category,
      'field': field,
      'suggestion': suggestion,
    };
  }
}

/// Configuration validation severity levels
enum ConfigValidationSeverity {
  error,
  warning,
  info,
}

/// Configuration validation categories
enum ConfigValidationCategory {
  security('Security'),
  network('Network'),
  authentication('Authentication'),
  database('Database'),
  performance('Performance'),
  compatibility('Compatibility'),
  deployment('Deployment'),
  feature('Feature'),
  environment('Environment');

  const ConfigValidationCategory(this.displayName);
  final String displayName;
}

/// Comprehensive configuration validator for CarNow authentication system
class ConfigValidator {
  static ConfigValidator? _instance;
  static ConfigValidator get instance => _instance ??= ConfigValidator._();

  ConfigValidator._();

  /// Validate complete configuration
  Future<ConfigValidationResult> validateConfiguration() async {
    final errors = <ConfigValidationError>[];
    final warnings = <ConfigValidationWarning>[];
    final metadata = <String, dynamic>{};

    try {
      // Validate environment configuration
      final envValidation = await _validateEnvironmentConfig();
      errors.addAll(envValidation.errors);
      warnings.addAll(envValidation.warnings);
      metadata.addAll(envValidation.metadata);

      // Validate secure configuration
      final secureValidation = await _validateSecureConfig();
      errors.addAll(secureValidation.errors);
      warnings.addAll(secureValidation.warnings);
      metadata.addAll(secureValidation.metadata);

      // Validate feature flags
      final featureValidation = await _validateFeatureFlags();
      errors.addAll(featureValidation.errors);
      warnings.addAll(featureValidation.warnings);
      metadata.addAll(featureValidation.metadata);

      // Validate network connectivity
      final networkValidation = await _validateNetworkConfig();
      errors.addAll(networkValidation.errors);
      warnings.addAll(networkValidation.warnings);
      metadata.addAll(networkValidation.metadata);

      // Validate deployment readiness
      final deploymentValidation = await _validateDeploymentReadiness();
      errors.addAll(deploymentValidation.errors);
      warnings.addAll(deploymentValidation.warnings);
      metadata.addAll(deploymentValidation.metadata);

    } catch (e) {
      errors.add(ConfigValidationError(
        message: 'Configuration validation failed: $e',
        category: ConfigValidationCategory.environment.displayName,
        severity: ConfigValidationSeverity.error,
      ));
    }

    return ConfigValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
      metadata: metadata,
    );
  }

  /// Validate environment configuration
  Future<ConfigValidationResult> _validateEnvironmentConfig() async {
    final errors = <ConfigValidationError>[];
    final warnings = <ConfigValidationWarning>[];
    final metadata = <String, dynamic>{};

    try {
      final config = ConfigurationManager.current;
      metadata['environment'] = config.environment.name;
      metadata['environment_config'] = config.toJson();

      // Validate basic configuration
      final basicErrors = config.validate();
      for (final error in basicErrors) {
        errors.add(ConfigValidationError(
          message: error,
          category: ConfigValidationCategory.environment.displayName,
          suggestion: _getSuggestionForError(error),
        ));
      }

      // Validate URLs
      if (!_isValidUrl(config.apiBaseUrl)) {
        errors.add(ConfigValidationError(
          message: 'Invalid API base URL format',
          category: ConfigValidationCategory.network.displayName,
          field: 'apiBaseUrl',
          suggestion: 'Ensure URL includes protocol (http:// or https://)',
        ));
      }

      if (!_isValidUrl(config.supabaseUrl)) {
        errors.add(ConfigValidationError(
          message: 'Invalid Supabase URL format',
          category: ConfigValidationCategory.database.displayName,
          field: 'supabaseUrl',
          suggestion: 'Ensure Supabase URL is properly formatted',
        ));
      }

      // Validate timeouts
      if (config.apiTimeoutSeconds < 5) {
        warnings.add(ConfigValidationWarning(
          message: 'API timeout is very short, may cause request failures',
          category: ConfigValidationCategory.performance.displayName,
          field: 'apiTimeoutSeconds',
          suggestion: 'Consider using at least 10 seconds for API timeout',
        ));
      }

      if (config.apiTimeoutSeconds > 120) {
        warnings.add(ConfigValidationWarning(
          message: 'API timeout is very long, may impact user experience',
          category: ConfigValidationCategory.performance.displayName,
          field: 'apiTimeoutSeconds',
          suggestion: 'Consider reducing API timeout to under 60 seconds',
        ));
      }

      // Environment-specific validations
      await _validateEnvironmentSpecific(config, errors, warnings);

    } catch (e) {
      errors.add(ConfigValidationError(
        message: 'Environment configuration validation failed: $e',
        category: ConfigValidationCategory.environment.displayName,
      ));
    }

    return ConfigValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
      metadata: metadata,
    );
  }

  /// Validate secure configuration
  Future<ConfigValidationResult> _validateSecureConfig() async {
    final errors = <ConfigValidationError>[];
    final warnings = <ConfigValidationWarning>[];
    final metadata = <String, dynamic>{};

    try {
      await SecureConfigManager.instance.initialize();
      final secureConfig = SecureConfigManager.instance;
      
      metadata['secure_config_status'] = secureConfig.getConfigurationStatus();

      // Validate configuration completeness
      if (!secureConfig.isConfigurationComplete) {
        errors.add(ConfigValidationError(
          message: 'Secure configuration is incomplete',
          category: ConfigValidationCategory.security.displayName,
          suggestion: 'Ensure all required API keys and secrets are configured',
        ));
      }

      // Validate individual secure configuration items
      final secureValidationErrors = secureConfig.validateConfiguration();
      for (final error in secureValidationErrors) {
        errors.add(ConfigValidationError(
          message: error,
          category: ConfigValidationCategory.security.displayName,
          suggestion: _getSuggestionForSecureConfigError(error),
        ));
      }

      // Validate key formats
      final supabaseKey = secureConfig.supabaseAnonKey;
      if (supabaseKey != null && !_isValidSupabaseKey(supabaseKey)) {
        errors.add(ConfigValidationError(
          message: 'Supabase anonymous key format is invalid',
          category: ConfigValidationCategory.authentication.displayName,
          field: 'supabaseAnonKey',
          suggestion: 'Ensure Supabase key is a valid JWT token',
        ));
      }

      final googleClientId = secureConfig.googleClientId;
      if (googleClientId != null && !_isValidGoogleClientId(googleClientId)) {
        errors.add(ConfigValidationError(
          message: 'Google client ID format is invalid',
          category: ConfigValidationCategory.authentication.displayName,
          field: 'googleClientId',
          suggestion: 'Ensure Google client ID ends with .apps.googleusercontent.com',
        ));
      }

      // Check for placeholder values
      if (supabaseKey?.contains('PLACEHOLDER') == true) {
        errors.add(ConfigValidationError(
          message: 'Supabase anonymous key contains placeholder value',
          category: ConfigValidationCategory.security.displayName,
          field: 'supabaseAnonKey',
          suggestion: 'Replace placeholder with actual Supabase anonymous key',
        ));
      }

      if (googleClientId?.contains('PLACEHOLDER') == true) {
        errors.add(ConfigValidationError(
          message: 'Google client ID contains placeholder value',
          category: ConfigValidationCategory.security.displayName,
          field: 'googleClientId',
          suggestion: 'Replace placeholder with actual Google client ID',
        ));
      }

    } catch (e) {
      errors.add(ConfigValidationError(
        message: 'Secure configuration validation failed: $e',
        category: ConfigValidationCategory.security.displayName,
      ));
    }

    return ConfigValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
      metadata: metadata,
    );
  }

  /// Validate feature flags configuration
  Future<ConfigValidationResult> _validateFeatureFlags() async {
    final errors = <ConfigValidationError>[];
    final warnings = <ConfigValidationWarning>[];
    final metadata = <String, dynamic>{};

    try {
      await FeatureFlagsManager.instance.initialize();
      final flagsManager = FeatureFlagsManager.instance;
      
      metadata['feature_flags_status'] = flagsManager.getStatus();

      // Validate critical feature flags
      if (!FeatureFlag.emailAuth.isEnabled && !FeatureFlag.googleAuth.isEnabled) {
        errors.add(ConfigValidationError(
          message: 'No authentication methods are enabled',
          category: ConfigValidationCategory.authentication.displayName,
          suggestion: 'Enable at least one authentication method (email or Google)',
        ));
      }

      // Environment-specific feature flag validations
      final currentEnv = ConfigurationManager.current.environment;
      
      if (currentEnv == Environment.production) {
        if (FeatureFlag.debugLogging.isEnabled) {
          warnings.add(ConfigValidationWarning(
            message: 'Debug logging is enabled in production',
            category: ConfigValidationCategory.security.displayName,
            suggestion: 'Consider disabling debug logging in production',
          ));
        }

        if (FeatureFlag.experimentalFeatures.isEnabled) {
          warnings.add(ConfigValidationWarning(
            message: 'Experimental features are enabled in production',
            category: ConfigValidationCategory.deployment.displayName,
            suggestion: 'Disable experimental features in production',
          ));
        }
      }

      // Check for conflicting feature flags
      if (FeatureFlag.enhancedSecurity.isEnabled && !FeatureFlag.rateLimiting.isEnabled) {
        warnings.add(ConfigValidationWarning(
          message: 'Enhanced security is enabled but rate limiting is disabled',
          category: ConfigValidationCategory.security.displayName,
          suggestion: 'Consider enabling rate limiting for better security',
        ));
      }

    } catch (e) {
      errors.add(ConfigValidationError(
        message: 'Feature flags validation failed: $e',
        category: ConfigValidationCategory.feature.displayName,
      ));
    }

    return ConfigValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
      metadata: metadata,
    );
  }

  /// Validate network configuration
  Future<ConfigValidationResult> _validateNetworkConfig() async {
    final errors = <ConfigValidationError>[];
    final warnings = <ConfigValidationWarning>[];
    final metadata = <String, dynamic>{};

    try {
      final config = ConfigurationManager.current;
      
      // Test API connectivity (basic check)
      final apiConnectivity = await _testConnectivity(config.apiBaseUrl);
      metadata['api_connectivity'] = apiConnectivity;
      
      if (!apiConnectivity['reachable']) {
        if (config.environment == Environment.production) {
          errors.add(ConfigValidationError(
            message: 'API endpoint is not reachable',
            category: ConfigValidationCategory.network.displayName,
            field: 'apiBaseUrl',
            suggestion: 'Verify API server is running and accessible',
          ));
        } else {
          warnings.add(ConfigValidationWarning(
            message: 'API endpoint is not reachable',
            category: ConfigValidationCategory.network.displayName,
            field: 'apiBaseUrl',
            suggestion: 'Start local development server or check network connection',
          ));
        }
      }

      // Test Supabase connectivity
      final supabaseConnectivity = await _testConnectivity(config.supabaseUrl);
      metadata['supabase_connectivity'] = supabaseConnectivity;
      
      if (!supabaseConnectivity['reachable']) {
        errors.add(ConfigValidationError(
          message: 'Supabase endpoint is not reachable',
          category: ConfigValidationCategory.database.displayName,
          field: 'supabaseUrl',
          suggestion: 'Check Supabase project status and network connection',
        ));
      }

    } catch (e) {
      warnings.add(ConfigValidationWarning(
        message: 'Network connectivity validation failed: $e',
        category: ConfigValidationCategory.network.displayName,
        suggestion: 'Check network connection and try again',
      ));
    }

    return ConfigValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
      metadata: metadata,
    );
  }

  /// Validate deployment readiness
  Future<ConfigValidationResult> _validateDeploymentReadiness() async {
    final errors = <ConfigValidationError>[];
    final warnings = <ConfigValidationWarning>[];
    final metadata = <String, dynamic>{};

    try {
      final config = ConfigurationManager.current;
      metadata['deployment_environment'] = config.environment.name;

      // Production readiness checks
      if (config.environment == Environment.production) {
        // Check for development-only configurations
        if (config.apiBaseUrl.contains('localhost') || config.apiBaseUrl.contains('127.0.0.1')) {
          errors.add(ConfigValidationError(
            message: 'Production environment using localhost API URL',
            category: ConfigValidationCategory.deployment.displayName,
            field: 'apiBaseUrl',
            suggestion: 'Use production API URL for production environment',
          ));
        }

        // Check security configurations
        if (!FeatureFlag.enhancedSecurity.isEnabled) {
          warnings.add(ConfigValidationWarning(
            message: 'Enhanced security features are disabled in production',
            category: ConfigValidationCategory.security.displayName,
            suggestion: 'Enable enhanced security features for production',
          ));
        }

        // Check monitoring configurations
        if (!FeatureFlag.analytics.isEnabled) {
          warnings.add(ConfigValidationWarning(
            message: 'Analytics are disabled in production',
            category: ConfigValidationCategory.deployment.displayName,
            suggestion: 'Enable analytics for production monitoring',
          ));
        }
      }

      // Development environment checks
      if (config.environment == Environment.development) {
        if (!config.enableDebugLogging) {
          warnings.add(ConfigValidationWarning(
            message: 'Debug logging is disabled in development',
            category: ConfigValidationCategory.deployment.displayName,
            suggestion: 'Enable debug logging for development',
          ));
        }
      }

      // Platform-specific checks
      if (Platform.isIOS) {
        metadata['platform'] = 'iOS';
        // iOS-specific validations
      } else if (Platform.isAndroid) {
        metadata['platform'] = 'Android';
        // Android-specific validations
      }

    } catch (e) {
      errors.add(ConfigValidationError(
        message: 'Deployment readiness validation failed: $e',
        category: ConfigValidationCategory.deployment.displayName,
      ));
    }

    return ConfigValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
      metadata: metadata,
    );
  }

  /// Validate environment-specific configurations
  Future<void> _validateEnvironmentSpecific(
    EnvironmentConfig config,
    List<ConfigValidationError> errors,
    List<ConfigValidationWarning> warnings,
  ) async {
    switch (config.environment) {
      case Environment.development:
        // Development-specific validations
        if (!config.enableDebugLogging) {
          warnings.add(ConfigValidationWarning(
            message: 'Debug logging is disabled in development environment',
            category: ConfigValidationCategory.environment.displayName,
            suggestion: 'Enable debug logging for better development experience',
          ));
        }
        break;

      case Environment.production:
        // Production-specific validations
        if (config.enableDebugLogging) {
          warnings.add(ConfigValidationWarning(
            message: 'Debug logging is enabled in production environment',
            category: ConfigValidationCategory.security.displayName,
            suggestion: 'Disable debug logging in production for security',
          ));
        }
        
        if (config.apiTimeoutSeconds > 30) {
          warnings.add(ConfigValidationWarning(
            message: 'API timeout is high for production environment',
            category: ConfigValidationCategory.performance.displayName,
            suggestion: 'Use shorter timeout for better user experience',
          ));
        }
        break;

      case Environment.staging:
        // Staging-specific validations
        if (!config.enableAnalytics) {
          warnings.add(ConfigValidationWarning(
            message: 'Analytics are disabled in staging environment',
            category: ConfigValidationCategory.environment.displayName,
            suggestion: 'Enable analytics in staging to test production behavior',
          ));
        }
        break;

      case Environment.testing:
        // Testing-specific validations
        if (config.enableAnalytics) {
          warnings.add(ConfigValidationWarning(
            message: 'Analytics are enabled in testing environment',
            category: ConfigValidationCategory.environment.displayName,
            suggestion: 'Disable analytics in testing to avoid test data pollution',
          ));
        }
        break;
    }
  }

  /// Test network connectivity to an endpoint
  Future<Map<String, dynamic>> _testConnectivity(String url) async {
    try {
      final uri = Uri.parse(url);
      final client = HttpClient();
      client.connectionTimeout = const Duration(seconds: 5);
      
      final request = await client.getUrl(uri);
      final response = await request.close();
      
      client.close();
      
      return {
        'reachable': true,
        'status_code': response.statusCode,
        'response_time': DateTime.now().millisecondsSinceEpoch,
      };
    } catch (e) {
      return {
        'reachable': false,
        'error': e.toString(),
      };
    }
  }

  /// Check if URL is valid
  bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && uri.hasAuthority;
    } catch (e) {
      return false;
    }
  }

  /// Check if Supabase key is valid format
  bool _isValidSupabaseKey(String key) {
    return key.startsWith('eyJ') && key.split('.').length == 3;
  }

  /// Check if Google client ID is valid format
  bool _isValidGoogleClientId(String clientId) {
    return clientId.contains('.apps.googleusercontent.com');
  }

  /// Get suggestion for configuration error
  String? _getSuggestionForError(String error) {
    if (error.contains('API base URL')) {
      return 'Ensure API base URL includes protocol (http:// or https://)';
    }
    if (error.contains('Supabase URL')) {
      return 'Check Supabase project URL in project settings';
    }
    if (error.contains('encryption key')) {
      return 'Set ENCRYPTION_KEY environment variable';
    }
    if (error.contains('timeout')) {
      return 'Use positive timeout values';
    }
    return null;
  }

  /// Get suggestion for secure configuration error
  String? _getSuggestionForSecureConfigError(String error) {
    if (error.contains('Supabase anonymous key')) {
      return 'Get anonymous key from Supabase project settings';
    }
    if (error.contains('Google client ID')) {
      return 'Get client ID from Google Cloud Console';
    }
    if (error.contains('encryption key')) {
      return 'Generate a secure 32-character encryption key';
    }
    return null;
  }

  /// Generate configuration report
  Future<Map<String, dynamic>> generateConfigurationReport() async {
    final validation = await validateConfiguration();
    
    return {
      'timestamp': DateTime.now().toIso8601String(),
      'validation_result': {
        'is_valid': validation.isValid,
        'summary': validation.summary,
        'errors_count': validation.errors.length,
        'warnings_count': validation.warnings.length,
      },
      'errors': validation.errors.map((e) => e.toJson()).toList(),
      'warnings': validation.warnings.map((w) => w.toJson()).toList(),
      'metadata': validation.metadata,
      'environment': ConfigurationManager.current.environment.name,
      'feature_flags': FeatureFlagsManager.instance.getAllFlags(),
    };
  }
}
