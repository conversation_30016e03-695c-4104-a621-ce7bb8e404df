import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/auth/unified_auth_provider.dart';
import '../networking/simple_api_client.dart';
import '../models/ticket_message_model.dart';
import '../models/ticket_model.dart';

part 'support_ticket_provider.g.dart';

/// Clean Support Ticket Providers - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// FIXED: Removed direct Supabase imports and complex patterns
/// ✅ Uses SimpleApiClient ONLY
/// ✅ Uses SimpleAuthSystem for auth  
/// ✅ Simple @riverpod patterns only

// =============================================================================
// TASK 9: Clean Support Ticket Providers
// =============================================================================

/// User's support tickets provider - simple and clean
@riverpod
Future<List<TicketModel>> userSupportTickets(Ref ref) async {
  final isAuthenticated = ref.watch(isAuthenticatedProvider);
  final currentUser = ref.watch(currentUserProvider);

  if (!isAuthenticated || currentUser?.id == null) {
    return [];
  }

  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/support/tickets',
    );
    
    if (!response.isSuccess || response.data == null) {
      return [];
    }

    final data = response.data!;
    final List<dynamic> ticketsJson = data.containsKey('tickets') && data['tickets'] is List
        ? data['tickets'] as List<dynamic>
        : data.containsKey('data') && data['data'] is List
            ? data['data'] as List<dynamic>
            : [];

    return ticketsJson
        .map((json) => TicketModel.fromJson(json as Map<String, dynamic>))
        .toList();
  } catch (e) {
    return [];
  }
}

/// Single support ticket by ID provider
@riverpod
Future<TicketModel?> supportTicketById(Ref ref, String ticketId) async {
  final isAuthenticated = ref.watch(isAuthenticatedProvider);

  if (!isAuthenticated) {
    return null;
  }

  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/support/tickets/$ticketId',
    );
    
    if (!response.isSuccess || response.data == null) {
      return null;
    }

    return TicketModel.fromJson(response.data!);
  } catch (e) {
    return null;
  }
}

/// Ticket messages provider
@riverpod
Future<List<TicketMessageModel>> ticketMessages(Ref ref, String ticketId) async {
  final isAuthenticated = ref.watch(isAuthenticatedProvider);

  if (!isAuthenticated) {
    return [];
  }

  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/support/tickets/$ticketId/messages',
    );
    
    if (!response.isSuccess || response.data == null) {
      return [];
    }

    final data = response.data!;
    final List<dynamic> messagesJson = data.containsKey('messages') && data['messages'] is List
        ? data['messages'] as List<dynamic>
        : data.containsKey('data') && data['data'] is List
            ? data['data'] as List<dynamic>
            : [];

    return messagesJson
        .map((json) => TicketMessageModel.fromJson(json as Map<String, dynamic>))
        .toList();
  } catch (e) {
    return [];
  }
}

/// Total unread tickets count provider
@riverpod
Future<int> totalUnreadTicketsCount(Ref ref) async {
  final tickets = await ref.watch(userSupportTicketsProvider.future);
  
  return tickets.where((ticket) => 
    ticket.status != 'closed' && 
    ticket.hasUnreadMessages == true
  ).length;
}

/// Support ticket actions provider
@riverpod
class SupportTicketActions extends _$SupportTicketActions {
  @override
  Future<List<TicketModel>> build() async {
    return await ref.read(userSupportTicketsProvider.future);
  }

  /// Create a new support ticket
  Future<TicketModel> createTicket({
    required String subject,
    required String description,
    required String category,
    String priority = 'medium',
  }) async {
    final isAuthenticated = ref.read(isAuthenticatedProvider);
    final currentUser = ref.read(currentUserProvider);

    if (!isAuthenticated || currentUser?.id == null) {
      throw Exception('User not authenticated');
    }

    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.post<Map<String, dynamic>>(
      '/support/tickets',
      data: {
        'subject': subject,
        'description': description,
        'category': category,
        'priority': priority,
      },
    );
    
    if (!response.isSuccess || response.data == null) {
      throw Exception('Failed to create ticket: ${response.message}');
    }

    final ticket = TicketModel.fromJson(response.data!);

    // Refresh the tickets list
    ref.invalidate(userSupportTicketsProvider);

    return ticket;
  }

  /// Update ticket status
  Future<TicketModel> updateTicketStatus(String ticketId, String status) async {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.put<Map<String, dynamic>>(
      '/support/tickets/$ticketId/status',
      data: {'status': status},
    );
    
    if (!response.isSuccess || response.data == null) {
      throw Exception('Failed to update ticket status: ${response.message}');
    }

    final ticket = TicketModel.fromJson(response.data!);

    // Refresh the tickets list
    ref.invalidate(userSupportTicketsProvider);
    ref.invalidate(supportTicketByIdProvider(ticketId));

    return ticket;
  }

  /// Add message to ticket
  Future<TicketMessageModel> addMessage(String ticketId, String message) async {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.post<Map<String, dynamic>>(
      '/support/tickets/$ticketId/messages',
      data: {'message': message},
    );
    
    if (!response.isSuccess || response.data == null) {
      throw Exception('Failed to add message: ${response.message}');
    }

    final ticketMessage = TicketMessageModel.fromJson(response.data!);

    // Refresh the messages list
    ref.invalidate(ticketMessagesProvider(ticketId));

    return ticketMessage;
  }

  /// Close ticket
  Future<void> closeTicket(String ticketId) async {
    await updateTicketStatus(ticketId, 'closed');
  }

  /// Reopen ticket
  Future<void> reopenTicket(String ticketId) async {
    await updateTicketStatus(ticketId, 'open');
  }
}

// =============================================================================
// DEPRECATED: Legacy Complex Providers - Mark for Removal
// =============================================================================

/// DEPRECATED: Use userSupportTicketsProvider instead
@Deprecated('Use userSupportTicketsProvider instead - this had complex StateNotifier patterns')
final legacySupportTicketsProvider = Provider<List<TicketModel>>((ref) => []);

/// DEPRECATED: Use supportTicketActionsProvider instead
@Deprecated('Use supportTicketActionsProvider instead - this had complex hierarchy')
final legacyTicketActionsProvider = Provider<void>((ref) {});

// =============================================================================
// REMOVED: Complex StateNotifier Patterns
// =============================================================================
// The following complex patterns have been REMOVED:
// ❌ Complex StateNotifierProvider with UserSupportTickets class
// ❌ Direct Supabase imports and usage
// ❌ Complex async stream mapping with ref.mounted checks
// ❌ Complex repository pattern dependencies
// ❌ Multiple provider disposal chains
//
// Replaced with:
// ✅ Simple @riverpod functions
// ✅ SimpleApiClient for ALL data operations
// ✅ SimpleAuthSystem for auth state
// ✅ Clean error handling with try/catch
// ✅ Easy to test and maintain patterns
