/// ============================================================================
/// NETWORK RETRY MANAGER - Advanced Network Retry System
/// ============================================================================
/// 
/// Network retry mechanisms with exponential backoff and failure handling
/// Task 21: Enhance error handling and user feedback - Network retry
/// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
/// ============================================================================
library;

import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../error/centralized_error_handler.dart';
import '../loading/advanced_loading_states.dart';
import '../theme/carnow_colors.dart';

/// Network retry configuration
class NetworkRetryConfig {
  const NetworkRetryConfig({
    this.maxRetries = 3,
    this.baseDelay = const Duration(seconds: 1),
    this.maxDelay = const Duration(seconds: 30),
    this.backoffMultiplier = 2.0,
    this.jitterFactor = 0.1,
    this.retryableStatusCodes = const {408, 429, 500, 502, 503, 504},
    this.retryableExceptions = const {
      SocketException,
      TimeoutException,
      HttpException,
    },
  });

  final int maxRetries;
  final Duration baseDelay;
  final Duration maxDelay;
  final double backoffMultiplier;
  final double jitterFactor;
  final Set<int> retryableStatusCodes;
  final Set<Type> retryableExceptions;

  /// Check if error is retryable
  bool isRetryable(Object error) {
    // Check exception types
    for (final exceptionType in retryableExceptions) {
      if (error.runtimeType == exceptionType) {
        return true;
      }
    }

    // Check HTTP status codes
    if (error is HttpException) {
      final statusCode = _extractStatusCode(error.message);
      if (statusCode != null && retryableStatusCodes.contains(statusCode)) {
        return true;
      }
    }

    // Check error message patterns
    final errorMessage = error.toString().toLowerCase();
    if (errorMessage.contains('network') ||
        errorMessage.contains('connection') ||
        errorMessage.contains('timeout') ||
        errorMessage.contains('unreachable')) {
      return true;
    }

    return false;
  }

  int? _extractStatusCode(String message) {
    final regex = RegExp(r'(\d{3})');
    final match = regex.firstMatch(message);
    return match != null ? int.tryParse(match.group(1)!) : null;
  }

  /// Calculate delay for retry attempt
  Duration calculateDelay(int attemptNumber) {
    final exponentialDelay = baseDelay.inMilliseconds * 
        pow(backoffMultiplier, attemptNumber - 1);
    
    // Add jitter to prevent thundering herd
    final jitter = exponentialDelay * jitterFactor * (Random().nextDouble() - 0.5);
    final delayWithJitter = exponentialDelay + jitter;
    
    // Cap at max delay
    final cappedDelay = min(delayWithJitter, maxDelay.inMilliseconds.toDouble());
    
    return Duration(milliseconds: cappedDelay.round());
  }
}

/// Network retry result
class NetworkRetryResult<T> {
  const NetworkRetryResult({
    required this.success,
    this.data,
    this.error,
    this.attemptCount = 0,
    this.totalDuration,
  });

  final bool success;
  final T? data;
  final Object? error;
  final int attemptCount;
  final Duration? totalDuration;

  factory NetworkRetryResult.success(T data, {
    required int attemptCount,
    Duration? totalDuration,
  }) {
    return NetworkRetryResult(
      success: true,
      data: data,
      attemptCount: attemptCount,
      totalDuration: totalDuration,
    );
  }

  factory NetworkRetryResult.failure(Object error, {
    required int attemptCount,
    Duration? totalDuration,
  }) {
    return NetworkRetryResult(
      success: false,
      error: error,
      attemptCount: attemptCount,
      totalDuration: totalDuration,
    );
  }
}

/// Network retry manager
class NetworkRetryManager {
  NetworkRetryManager({
    this.config = const NetworkRetryConfig(),
    this.errorHandler,
    this.loadingNotifier,
  });

  final NetworkRetryConfig config;
  final CentralizedErrorHandler? errorHandler;
  final AdvancedLoadingNotifier? loadingNotifier;

  /// Execute operation with retry logic
  Future<NetworkRetryResult<T>> executeWithRetry<T>(
    Future<T> Function() operation, {
    String? operationName,
    NetworkRetryConfig? customConfig,
    void Function(int attemptNumber, Object error)? onRetry,
    void Function(Duration delay)? onDelay,
  }) async {
    final effectiveConfig = customConfig ?? config;
    final startTime = DateTime.now();
    int attemptCount = 0;
    Object? lastError;

    // Start loading state
    loadingNotifier?.startLoading(
      message: operationName != null ? 'تنفيذ $operationName...' : 'جاري المعالجة...',
      operation: operationName,
      canCancel: false,
    );

    while (attemptCount <= effectiveConfig.maxRetries) {
      attemptCount++;
      
      try {
        // Update loading progress
        if (loadingNotifier != null && effectiveConfig.maxRetries > 0) {
          final progress = (attemptCount - 1) / effectiveConfig.maxRetries;
          loadingNotifier!.updateProgress(
            progress,
            message: attemptCount > 1 
                ? 'المحاولة $attemptCount من ${effectiveConfig.maxRetries + 1}...'
                : null,
          );
        }

        final result = await operation();
        
        // Success - complete loading
        loadingNotifier?.completeSuccess(
          message: operationName != null ? 'تم $operationName بنجاح' : 'تمت العملية بنجاح',
        );

        final totalDuration = DateTime.now().difference(startTime);
        
        if (kDebugMode && attemptCount > 1) {
          debugPrint(
            'NetworkRetry: Operation succeeded after $attemptCount attempts in ${totalDuration.inMilliseconds}ms'
          );
        }

        return NetworkRetryResult.success(
          result,
          attemptCount: attemptCount,
          totalDuration: totalDuration,
        );
        
      } catch (error) {
        lastError = error;
        
        // Log error
        if (kDebugMode) {
          debugPrint('NetworkRetry: Attempt $attemptCount failed: $error');
        }

        // Handle error with centralized handler
        errorHandler?.handleError(
          error,
          context: 'network_retry_attempt_$attemptCount',
          showToUser: false,
        );

        // Check if we should retry
        if (attemptCount > effectiveConfig.maxRetries || 
            !effectiveConfig.isRetryable(error)) {
          break;
        }

        // Calculate delay for next attempt
        final delay = effectiveConfig.calculateDelay(attemptCount);
        
        // Notify about retry
        onRetry?.call(attemptCount, error);
        
        // Update loading state to retrying
        loadingNotifier?.startRetry(
          message: 'إعادة المحاولة بعد ${_formatDelay(delay)}...',
        );

        if (kDebugMode) {
          debugPrint(
            'NetworkRetry: Retrying in ${delay.inMilliseconds}ms (attempt ${attemptCount + 1})'
          );
        }

        // Wait before retry
        onDelay?.call(delay);
        await Future.delayed(delay);
      }
    }

    // All retries failed
    final totalDuration = DateTime.now().difference(startTime);
    
    // Complete loading with error
    loadingNotifier?.completeError(
      lastError!,
      message: 'فشلت العملية بعد $attemptCount محاولات',
    );

    // Handle final error
    errorHandler?.handleError(
      lastError!,
      context: 'network_retry_final_failure',
      showToUser: true,
    );

    if (kDebugMode) {
      debugPrint(
        'NetworkRetry: Operation failed after $attemptCount attempts in ${totalDuration.inMilliseconds}ms'
      );
    }

    return NetworkRetryResult.failure(
      lastError!,
      attemptCount: attemptCount,
      totalDuration: totalDuration,
    );
  }

  /// Execute operation with timeout and retry
  Future<NetworkRetryResult<T>> executeWithTimeoutAndRetry<T>(
    Future<T> Function() operation, {
    Duration timeout = const Duration(seconds: 30),
    String? operationName,
    NetworkRetryConfig? customConfig,
    void Function(int attemptNumber, Object error)? onRetry,
  }) async {
    return executeWithRetry(
      () => operation().timeout(timeout),
      operationName: operationName,
      customConfig: customConfig,
      onRetry: onRetry,
    );
  }

  String _formatDelay(Duration delay) {
    if (delay.inSeconds < 60) {
      return '${delay.inSeconds} ثانية';
    } else {
      final minutes = delay.inMinutes;
      final seconds = delay.inSeconds % 60;
      return '$minutesد $secondsث';
    }
  }
}

/// Network retry state
class NetworkRetryState {
  const NetworkRetryState({
    this.isRetrying = false,
    this.attemptCount = 0,
    this.maxRetries = 0,
    this.nextRetryIn,
    this.lastError,
    this.operationName,
  });

  final bool isRetrying;
  final int attemptCount;
  final int maxRetries;
  final Duration? nextRetryIn;
  final Object? lastError;
  final String? operationName;

  NetworkRetryState copyWith({
    bool? isRetrying,
    int? attemptCount,
    int? maxRetries,
    Duration? nextRetryIn,
    Object? lastError,
    String? operationName,
  }) {
    return NetworkRetryState(
      isRetrying: isRetrying ?? this.isRetrying,
      attemptCount: attemptCount ?? this.attemptCount,
      maxRetries: maxRetries ?? this.maxRetries,
      nextRetryIn: nextRetryIn ?? this.nextRetryIn,
      lastError: lastError ?? this.lastError,
      operationName: operationName ?? this.operationName,
    );
  }
}

/// Network retry state notifier
class NetworkRetryNotifier extends StateNotifier<NetworkRetryState> {
  NetworkRetryNotifier() : super(const NetworkRetryState());

  Timer? _retryTimer;

  @override
  void dispose() {
    _retryTimer?.cancel();
    super.dispose();
  }

  /// Start retry countdown
  void startRetryCountdown({
    required int attemptCount,
    required int maxRetries,
    required Duration delay,
    Object? error,
    String? operationName,
  }) {
    _retryTimer?.cancel();
    
    state = NetworkRetryState(
      isRetrying: true,
      attemptCount: attemptCount,
      maxRetries: maxRetries,
      nextRetryIn: delay,
      lastError: error,
      operationName: operationName,
    );

    // Update countdown every second
    _retryTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      final remaining = state.nextRetryIn;
      if (remaining == null || remaining.inSeconds <= 1) {
        timer.cancel();
        state = state.copyWith(
          isRetrying: false,
          nextRetryIn: Duration.zero,
        );
      } else {
        state = state.copyWith(
          nextRetryIn: remaining - const Duration(seconds: 1),
        );
      }
    });
  }

  /// Stop retry
  void stopRetry() {
    _retryTimer?.cancel();
    state = const NetworkRetryState();
  }
}

/// Provider for network retry manager
final networkRetryManagerProvider = Provider<NetworkRetryManager>((ref) {
  final errorHandler = ref.read(centralizedErrorHandlerProvider);
  final loadingNotifier = ref.read(advancedLoadingProvider.notifier);
  
  return NetworkRetryManager(
    errorHandler: errorHandler,
    loadingNotifier: loadingNotifier,
  );
});

/// Provider for network retry state
final networkRetryStateProvider = StateNotifierProvider.autoDispose<NetworkRetryNotifier, NetworkRetryState>((ref) {
  return NetworkRetryNotifier();
});

/// Extension for easy retry operations
extension NetworkRetryExtension on Ref {
  /// Execute operation with retry
  Future<NetworkRetryResult<T>> executeWithRetry<T>(
    Future<T> Function() operation, {
    String? operationName,
    NetworkRetryConfig? config,
  }) async {
    final retryManager = read(networkRetryManagerProvider);
    return retryManager.executeWithRetry(
      operation,
      operationName: operationName,
      customConfig: config,
    );
  }

  /// Execute operation with timeout and retry
  Future<NetworkRetryResult<T>> executeWithTimeoutAndRetry<T>(
    Future<T> Function() operation, {
    Duration timeout = const Duration(seconds: 30),
    String? operationName,
    NetworkRetryConfig? config,
  }) async {
    final retryManager = read(networkRetryManagerProvider);
    return retryManager.executeWithTimeoutAndRetry(
      operation,
      timeout: timeout,
      operationName: operationName,
      customConfig: config,
    );
  }
}

/// Retry indicator widget
class NetworkRetryIndicator extends ConsumerWidget {
  const NetworkRetryIndicator({
    super.key,
    this.onManualRetry,
    this.showCountdown = true,
    this.showProgress = true,
  });

  final VoidCallback? onManualRetry;
  final bool showCountdown;
  final bool showProgress;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final retryState = ref.watch(networkRetryStateProvider);
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (!retryState.isRetrying) {
      return const SizedBox.shrink();
    }

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Icon(
                  Icons.refresh,
                  color: CarnowColors.warning,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'إعادة المحاولة ${retryState.attemptCount} من ${retryState.maxRetries}',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: colorScheme.onSurface,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            
            if (showProgress) ...[
              const SizedBox(height: 12),
              LinearProgressIndicator(
                value: retryState.maxRetries > 0 
                    ? retryState.attemptCount / retryState.maxRetries 
                    : null,
                valueColor: const AlwaysStoppedAnimation<Color>(CarnowColors.warning),
                backgroundColor: colorScheme.surfaceContainerHighest,
              ),
            ],
            
            if (showCountdown && retryState.nextRetryIn != null) ...[
              const SizedBox(height: 12),
              Text(
                'المحاولة التالية خلال ${retryState.nextRetryIn!.inSeconds} ثانية',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
            
            if (onManualRetry != null) ...[
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: onManualRetry,
                icon: const Icon(Icons.refresh, size: 18),
                label: const Text('إعادة المحاولة الآن'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: CarnowColors.warning,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
