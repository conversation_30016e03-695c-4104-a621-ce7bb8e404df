import 'dart:async';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';

import '../errors/app_error.dart';
import '../errors/result.dart';
import 'dead_letter_queue_service.dart';

part 'global_error_handler.g.dart';

final _logger = Logger('GlobalErrorHandler');

/// Global Error Handler for Phase 3.1.1: Flutter Error Handling
///
/// Features:
/// - Global error handling for all API calls
/// - User-friendly error messages and displays
/// - Retry mechanisms for failed requests
/// - Offline mode and graceful degradation
/// - Integration with dead letter queue for failed operations
@riverpod
GlobalErrorHandler globalErrorHandler(GlobalErrorHandlerRef ref) {
  return GlobalErrorHandler(ref: ref);
}

class GlobalErrorHandler {
  GlobalErrorHandler({required this.ref});

  final Ref ref;

  /// Execute an operation with comprehensive error handling
  Future<Result<T>> executeWithErrorHandling<T>(
    Future<T> Function() operation, {
    required String operationName,
    String? context,
    Map<String, dynamic>? operationData,
    bool enableRetry = true,
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
    bool enableOfflineQueue = true,
    bool showUserFriendlyErrors = true,
  }) async {
    var attempt = 0;
    Duration currentDelay = initialDelay;

    while (attempt < maxRetries) {
      attempt++;

      try {
        _logger.info('Executing $operationName (attempt $attempt/$maxRetries)');

        final result = await operation();

        if (attempt > 1) {
          _logger.info('$operationName succeeded after $attempt attempts');
        }

        return Result.success(result);
      } catch (error, stackTrace) {
        _logger.warning(
          '$operationName failed (attempt $attempt/$maxRetries): $error',
        );

        final appError = _convertToAppError(
          error,
          stackTrace: stackTrace,
          context: context ?? operationName,
          operationData: operationData,
        );

        // Check if we should retry
        final shouldRetry =
            enableRetry && _shouldRetryError(appError) && attempt < maxRetries;

        if (!shouldRetry) {
          // Final failure - handle based on error type
          await _handleFinalFailure(
            operationName: operationName,
            error: appError,
            attemptCount: attempt,
            operationData: operationData,
            enableOfflineQueue: enableOfflineQueue,
          );

          return Result.failure(appError);
        }

        // Wait before retry with exponential backoff
        if (attempt < maxRetries) {
          _logger.info(
            'Retrying $operationName in ${currentDelay.inSeconds}s...',
          );
          await Future.delayed(currentDelay);
          currentDelay = Duration(
            milliseconds: (currentDelay.inMilliseconds * 1.5).round(),
          );
        }
      }
    }

    // Should not reach here, but just in case
    final error = AppError.unexpected(
      message: 'Unexpected error in retry logic for $operationName',
      data: {'operationName': operationName, 'attempts': attempt},
    );

    return Result.failure(error);
  }

  /// Convert any error to AppError
  AppError _convertToAppError(
    dynamic error, {
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? operationData,
  }) {
    if (error is AppError) {
      return error;
    }

    if (error is DioException) {
      return _handleDioError(error, operationData);
    }

    if (error is SocketException) {
      return AppError.network(
        message: 'لا يوجد اتصال بالإنترنت',
        code: 'NO_INTERNET_CONNECTION',
        originalError: error,
        data: operationData,
      );
    }

    if (error is TimeoutException) {
      return AppError.network(
        message: 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى',
        code: 'REQUEST_TIMEOUT',
        originalError: error,
        data: operationData,
      );
    }

    if (error is FormatException) {
      return AppError.validation(
        message: 'تنسيق البيانات غير صحيح',
        code: 'INVALID_DATA_FORMAT',
        originalError: error,
        data: operationData,
      );
    }

    // Unknown error
    return AppError.unexpected(
      message: 'حدث خطأ غير متوقع',
      code: 'UNKNOWN_ERROR',
      originalError: error,
      data: {
        if (operationData != null) ...operationData,
        if (context != null) 'context': context,
        if (stackTrace != null) 'stackTrace': stackTrace.toString(),
        'errorType': error.runtimeType.toString(),
        'errorMessage': error.toString(),
      },
    );
  }

  /// Handle Dio-specific errors
  AppError _handleDioError(
    DioException error,
    Map<String, dynamic>? operationData,
  ) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return AppError.network(
          message: 'انتهت مهلة الاتصال',
          code: 'CONNECTION_TIMEOUT',
          originalError: error,
          data: operationData,
        );

      case DioExceptionType.connectionError:
        return AppError.network(
          message: 'فشل في الاتصال بالخادم',
          code: 'CONNECTION_ERROR',
          originalError: error,
          data: operationData,
        );

      case DioExceptionType.badResponse:
        return _handleHttpError(error, operationData);

      case DioExceptionType.cancel:
        return AppError.network(
          message: 'تم إلغاء الطلب',
          code: 'REQUEST_CANCELLED',
          originalError: error,
          data: operationData,
        );

      case DioExceptionType.unknown:
      default:
        return AppError.network(
          message: 'خطأ في الشبكة',
          code: 'NETWORK_ERROR',
          originalError: error,
          data: operationData,
        );
    }
  }

  /// Handle HTTP status code errors
  AppError _handleHttpError(
    DioException error,
    Map<String, dynamic>? operationData,
  ) {
    final statusCode = error.response?.statusCode ?? 0;
    final responseData = error.response?.data;

    switch (statusCode) {
      case 400:
        return AppError.validation(
          message: 'البيانات المرسلة غير صحيحة',
          code: 'BAD_REQUEST',
          originalError: error,
          data: {
            if (operationData != null) ...operationData,
            'statusCode': statusCode,
            'responseData': responseData,
          },
        );

      case 401:
        return AppError.authentication(
          message: 'انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى',
          code: 'UNAUTHORIZED',
          originalError: error,
          data: {
            if (operationData != null) ...operationData,
            'statusCode': statusCode,
          },
        );

      case 403:
        return AppError.authorization(
          message: 'ليس لديك صلاحية للقيام بهذا الإجراء',
          code: 'FORBIDDEN',
          originalError: error,
          data: {
            if (operationData != null) ...operationData,
            'statusCode': statusCode,
          },
        );

      case 404:
        return AppError.notFound(
          message: 'المورد المطلوب غير موجود',
          code: 'NOT_FOUND',
          originalError: error,
          data: {
            if (operationData != null) ...operationData,
            'statusCode': statusCode,
          },
        );

      case 409:
        return AppError.conflict(
          message: 'البيانات موجودة مسبقاً أو متضاربة',
          code: 'CONFLICT',
          originalError: error,
          data: {
            if (operationData != null) ...operationData,
            'statusCode': statusCode,
            'responseData': responseData,
          },
        );

      case 422:
        return AppError.validation(
          message: 'البيانات المرسلة لا تتوافق مع المتطلبات',
          code: 'UNPROCESSABLE_ENTITY',
          originalError: error,
          data: {
            if (operationData != null) ...operationData,
            'statusCode': statusCode,
            'responseData': responseData,
          },
        );

      case 429:
        return AppError.rateLimited(
          message: 'تم تجاوز الحد المسموح من الطلبات. يرجى المحاولة لاحقاً',
          code: 'RATE_LIMITED',
          originalError: error,
          data: {
            if (operationData != null) ...operationData,
            'statusCode': statusCode,
            'retryAfter': error.response?.headers['retry-after']?.first,
          },
        );

      case 500:
      case 502:
      case 503:
      case 504:
        return AppError.server(
          message: 'خطأ في الخادم. يرجى المحاولة لاحقاً',
          code: 'SERVER_ERROR',
          originalError: error,
          data: {
            if (operationData != null) ...operationData,
            'statusCode': statusCode,
          },
        );

      default:
        return AppError.network(
          message: 'خطأ في الشبكة ($statusCode)',
          code: 'HTTP_ERROR',
          originalError: error,
          data: {
            if (operationData != null) ...operationData,
            'statusCode': statusCode,
            'responseData': responseData,
          },
        );
    }
  }

  /// Determine if an error should be retried
  bool _shouldRetryError(AppError error) {
    // Don't retry validation, authentication, or authorization errors
    if (error.type == ErrorType.validation ||
        error.type == ErrorType.authentication ||
        error.type == ErrorType.authorization) {
      return false;
    }

    // Don't retry 4xx errors (except 408, 429)
    if (error.data?['statusCode'] != null) {
      final statusCode = error.data!['statusCode'] as int;
      if (statusCode >= 400 && statusCode < 500) {
        return statusCode == 408 ||
            statusCode == 429; // Timeout or Rate Limited
      }
    }

    // Retry network, server, and timeout errors
    return error.type == ErrorType.network ||
        error.type == ErrorType.server ||
        error.type == ErrorType.timeout ||
        error.code == 'CONNECTION_TIMEOUT' ||
        error.code == 'CONNECTION_ERROR' ||
        error.code == 'REQUEST_TIMEOUT';
  }

  /// Handle final failure after all retries
  Future<void> _handleFinalFailure({
    required String operationName,
    required AppError error,
    required int attemptCount,
    Map<String, dynamic>? operationData,
    bool enableOfflineQueue = true,
  }) async {
    _logger.severe(
      'Final failure for $operationName after $attemptCount attempts: ${error.message}',
    );

    // Simple error logging - Forever Plan Compliant (no complex offline queueing)
    if (enableOfflineQueue) {
      try {
        final deadLetterService = ref.read(deadLetterQueueServiceProvider);
        
        // Add to dead letter queue for analysis (simple logging only)
        await deadLetterService.addFailedOperation(
          operationName,
          error.message,
          attemptCount,
          operationData: operationData,
          endpoint: error.data?['endpoint'] as String?,
          userId: error.data?['userId'] as String?,
        );

        _logger.info('Added $operationName to dead letter queue for analysis');
      } catch (e) {
        _logger.severe('Failed to log final failure for $operationName: $e');
      }
    }
  }



  /// Show user-friendly error message
  void showErrorToUser(
    BuildContext context,
    AppError error, {
    VoidCallback? onRetry,
    VoidCallback? onDismiss,
  }) {
    if (!context.mounted) return;

    // Show different UI based on error type
    switch (error.type) {
      case ErrorType.network:
        _showNetworkErrorDialog(context, error, onRetry: onRetry);
        break;

      case ErrorType.authentication:
        _showAuthErrorDialog(context, error);
        break;

      case ErrorType.validation:
        _showValidationErrorSnackBar(context, error);
        break;

      default:
        _showGenericErrorSnackBar(context, error, onRetry: onRetry);
    }
  }

  /// Show network error dialog with retry option
  void _showNetworkErrorDialog(
    BuildContext context,
    AppError error, {
    VoidCallback? onRetry,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ في الاتصال'),
        content: Text(error.userMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          if (onRetry != null)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onRetry();
              },
              child: const Text('إعادة المحاولة'),
            ),
        ],
      ),
    );
  }

  /// Show authentication error dialog
  void _showAuthErrorDialog(BuildContext context, AppError error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ في المصادقة'),
        content: Text(error.userMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to login screen
              // Navigator.of(context).pushReplacementNamed('/login');
            },
            child: const Text('تسجيل الدخول'),
          ),
        ],
      ),
    );
  }

  /// Show validation error as snack bar
  void _showValidationErrorSnackBar(BuildContext context, AppError error) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(error.userMessage),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  /// Show generic error as snack bar
  void _showGenericErrorSnackBar(
    BuildContext context,
    AppError error, {
    VoidCallback? onRetry,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(error.userMessage),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
        action: onRetry != null
            ? SnackBarAction(
                label: 'إعادة المحاولة',
                textColor: Colors.white,
                onPressed: onRetry,
              )
            : null,
      ),
    );
  }
}

/// Extension to make error handling easier in providers
extension GlobalErrorHandlerExtension on Ref {
  /// Execute operation with global error handling
  Future<Result<T>> executeWithErrorHandling<T>(
    Future<T> Function() operation, {
    required String operationName,
    String? context,
    Map<String, dynamic>? operationData,
    bool enableRetry = true,
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
    bool enableOfflineQueue = true,
    bool showUserFriendlyErrors = true,
  }) {
    final handler = read(globalErrorHandlerProvider);
    return handler.executeWithErrorHandling(
      operation,
      operationName: operationName,
      context: context,
      operationData: operationData,
      enableRetry: enableRetry,
      maxRetries: maxRetries,
      initialDelay: initialDelay,
      enableOfflineQueue: enableOfflineQueue,
      showUserFriendlyErrors: showUserFriendlyErrors,
    );
  }

  /// Show error to user
  void showErrorToUser(
    BuildContext context,
    AppError error, {
    VoidCallback? onRetry,
    VoidCallback? onDismiss,
  }) {
    final handler = read(globalErrorHandlerProvider);
    handler.showErrorToUser(
      context,
      error,
      onRetry: onRetry,
      onDismiss: onDismiss,
    );
  }
}
