import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../config/backend_config.dart';
import '../auth/unified_auth_provider.dart';
import '../auth/auth_models.dart'; // Import AuthState models
import '../models/api_response.dart'; // Import the correct ApiResponse
import '../services/network_connectivity_service.dart';

part 'simple_api_client.g.dart';

/// Backend configuration provider
@riverpod
BackendConfig backendConfig(Ref ref) {
  return BackendConfig();
}

/// Simple API client for CarNow backend communication
/// Follows clean architecture: Flutter UI → Go Backend → Supabase Data
class SimpleApiClient {
  final Dio _dio;
  final Ref _ref;

  SimpleApiClient._internal({required Dio dio, required Ref ref})
      : _dio = dio,
        _ref = ref {
    _setupInterceptors();
  }

  /// Create a new SimpleApiClient instance with Ref
  factory SimpleApiClient({required Ref ref}) {
    return SimpleApiClient._internal(
      dio: Dio(BaseOptions(
        baseUrl: BackendConfig.baseUrlSync,
        connectTimeout: BackendConfig.requestTimeout,
        receiveTimeout: BackendConfig.requestTimeout,
        sendTimeout: BackendConfig.requestTimeout,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      )),
      ref: ref,
    );
  }

  /// Create a new SimpleApiClient instance with custom configuration
  factory SimpleApiClient.withConfig({
    required Ref ref,
    required String baseUrl,
    Duration? connectTimeout,
    Duration? receiveTimeout,
    Duration? sendTimeout,
    Map<String, dynamic>? headers,
  }) {
    return SimpleApiClient._internal(
      dio: Dio(BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: connectTimeout ?? BackendConfig.requestTimeout,
        receiveTimeout: receiveTimeout ?? BackendConfig.requestTimeout,
        sendTimeout: sendTimeout ?? BackendConfig.requestTimeout,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          ...?headers,
        },
      )),
      ref: ref,
    );
  }

  void _setupInterceptors() {
    _dio.interceptors.clear();
    
    // Request interceptor for adding auth headers
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // Add JWT token from auth system (deferred to avoid circular dependency)
          try {
            // Check if auth provider is initialized before trying to read token
            // This prevents circular dependency during initialization
            final authState = _ref.read(currentAuthStateProvider);
            // Only try to get token if auth provider is ready
            if (authState is! AuthStateInitial && authState is! AuthStateLoading) {
              try {
                final token = _ref.read(currentAccessTokenProvider);
                
                if (token != null) {
                  options.headers['Authorization'] = 'Bearer $token';
                }
              } catch (e) {
                // If auth provider is not ready yet, continue without token
                // This prevents circular dependency during initialization
                print('Auth token not available during request: $e');
              }
            }
          } catch (e) {
            // If auth provider is not ready yet, continue without token
            // This prevents circular dependency during initialization
            print('Auth token not available during request: $e');
          }
          
          // Add content type
          options.headers['Content-Type'] = 'application/json';
          
          handler.next(options);
        },
        onError: (error, handler) async {
          // Handle 401 errors by logging out user
          if (error.response?.statusCode == 401) {
            try {
              // Token is invalid, user needs to login again
              // Only try to sign out if auth provider is initialized
              final authState = _ref.read(currentAuthStateProvider);
              if (authState is! AuthStateInitial && authState is! AuthStateLoading) {
                try {
                  final authSystem = _ref.read(unifiedAuthProviderProvider.notifier);
                  await authSystem.signOut();
                } catch (signOutError) {
                  // Even sign out failed, just continue with error
                  print('Sign out failed: $signOutError');
                }
              }
            } catch (e) {
              // Even sign out failed, just continue with error
              print('Sign out failed: $e');
            }
          }
          handler.next(error);
        },
      ),
    );
  }

  /// GET request with proper error handling
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.get(
        endpoint,
        queryParameters: queryParameters,
      );
      
      return ApiResponse.success(
        response.data as T,
        message: 'Request successful',
      );
    } on DioException catch (e) {
      return _handleDioError<T>(e);
    } catch (e) {
      return ApiResponse.error(
        'Unexpected error: ${e.toString()}',
        message: 'Network request failed',
      );
    }
  }

  /// GET request with generic API pattern for backward compatibility
  Future<ApiResponse<T>> getApi<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
  }) async {
    return get<T>(endpoint, queryParameters: queryParameters);
  }

  /// POST request with generic API pattern for backward compatibility
  Future<ApiResponse<T>> postApi<T>(
    String endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) async {
    return post<T>(endpoint, data: data, queryParameters: queryParameters);
  }

  /// PUT request with generic API pattern for backward compatibility
  Future<ApiResponse<T>> putApi<T>(
    String endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) async {
    return put<T>(endpoint, data: data, queryParameters: queryParameters);
  }

  /// DELETE request with generic API pattern for backward compatibility
  Future<ApiResponse<T>> deleteApi<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
  }) async {
    return delete<T>(endpoint, queryParameters: queryParameters);
  }

  /// POST request with proper error handling
  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.post(
        endpoint,
        data: data,
        queryParameters: queryParameters,
      );
      
      return ApiResponse.success(
        response.data as T,
        message: 'Request successful',
      );
    } on DioException catch (e) {
      return _handleDioError<T>(e);
    } catch (e) {
      return ApiResponse.error(
        'Unexpected error: ${e.toString()}',
        message: 'Network request failed',
      );
    }
  }

  /// PUT request with proper error handling
  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.put(
        endpoint,
        data: data,
        queryParameters: queryParameters,
      );
      
      return ApiResponse.success(
        response.data as T,
        message: 'Request successful',
      );
    } on DioException catch (e) {
      return _handleDioError<T>(e);
    } catch (e) {
      return ApiResponse.error(
        'Unexpected error: ${e.toString()}',
        message: 'Network request failed',
      );
    }
  }

  /// DELETE request with proper error handling
  Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.delete(
        endpoint,
        queryParameters: queryParameters,
      );
      
      return ApiResponse.success(
        response.data as T,
        message: 'Request successful',
      );
    } on DioException catch (e) {
      return _handleDioError<T>(e);
    } catch (e) {
      return ApiResponse.error(
        'Unexpected error: ${e.toString()}',
        message: 'Network request failed',
      );
    }
  }

  /// PATCH request with proper error handling
  Future<ApiResponse<T>> patch<T>(
    String endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.patch(
        endpoint,
        data: data,
        queryParameters: queryParameters,
      );
      
      return ApiResponse.success(
        response.data as T,
        message: 'Request successful',
      );
    } on DioException catch (e) {
      return _handleDioError<T>(e);
    } catch (e) {
      return ApiResponse.error(
        'Unexpected error: ${e.toString()}',
        message: 'Network request failed',
      );
    }
  }

  /// Upload files with FormData - specialized method for file uploads
  Future<ApiResponse<T>> uploadFormData<T>(
    String endpoint,
    FormData formData, {
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.post(
        endpoint,
        data: formData,
        queryParameters: queryParameters,
      );
      
      return ApiResponse.success(
        response.data as T,
        message: 'Upload successful',
      );
    } on DioException catch (e) {
      return _handleDioError<T>(e);
    } catch (e) {
      return ApiResponse.error(
        'Unexpected error: ${e.toString()}',
        message: 'Upload failed',
      );
    }
  }

  /// Handle Dio errors and convert to ApiResponse
  ApiResponse<T> _handleDioError<T>(DioException error) {
    String errorMessage;
    
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        errorMessage = 'Connection timeout. Please check your internet connection.';
        break;
      case DioExceptionType.sendTimeout:
        errorMessage = 'Request timeout. Please try again.';
        break;
      case DioExceptionType.receiveTimeout:
        errorMessage = 'Server response timeout. Please try again.';
        break;
      case DioExceptionType.connectionError:
        errorMessage = 'Connection error. Please check your internet connection.';
        break;
      case DioExceptionType.badResponse:
        errorMessage = _extractErrorMessage(error.response);
        break;
      case DioExceptionType.cancel:
        errorMessage = 'Request was cancelled.';
        break;
      case DioExceptionType.unknown:
        errorMessage = 'An unexpected error occurred. Please try again.';
        break;
      default:
        errorMessage = 'Network error occurred. Please try again.';
    }
    
    return ApiResponse.error(
      errorMessage,
      message: 'Network request failed',
    );
  }

  /// Extract error message from response
  String _extractErrorMessage(Response? response) {
    if (response?.data is Map<String, dynamic>) {
      final data = response!.data as Map<String, dynamic>;
      return data['message'] ?? data['error'] ?? 'Server error occurred';
    }
    
    return 'Server error (${response?.statusCode ?? 'Unknown'})';
  }
}

/// Simple API client provider
@riverpod
SimpleApiClient simpleApiClient(Ref ref) {
  return SimpleApiClient(ref: ref);
}

/// Dio instance provider for lower-level access if needed
@riverpod
Dio dioInstance(Ref ref) {
  final dio = Dio(BaseOptions(
    baseUrl: BackendConfig.baseUrlSync,
    connectTimeout: BackendConfig.requestTimeout,
    receiveTimeout: BackendConfig.requestTimeout,
    sendTimeout: BackendConfig.requestTimeout,
  ));

  return dio;
}

/// Enhanced API client provider with global error handling
@riverpod
EnhancedApiClient enhancedApiClient(Ref ref) {
  final dio = ref.watch(dioInstanceProvider);
  return EnhancedApiClient(dio: dio, ref: ref);
}

/// Enhanced API client with global error handling integration
class EnhancedApiClient {
  const EnhancedApiClient({required this.dio, required this.ref});

  final Dio dio;
  final Ref ref;

  /// GET request with enhanced error handling
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await dio.get(endpoint, queryParameters: queryParameters);
      return ApiResponse.success(response.data as T);
    } on DioException catch (e) {
      return _handleError<T>(e);
    }
  }

  /// POST request with enhanced error handling
  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    Map<String, dynamic>? data,
  }) async {
    try {
      final response = await dio.post(endpoint, data: data);
      return ApiResponse.success(response.data as T);
    } on DioException catch (e) {
      return _handleError<T>(e);
    }
  }

  /// PUT request with enhanced error handling
  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    Map<String, dynamic>? data,
  }) async {
    try {
      final response = await dio.put(endpoint, data: data);
      return ApiResponse.success(response.data as T);
    } on DioException catch (e) {
      return _handleError<T>(e);
    }
  }

  /// DELETE request with enhanced error handling
  Future<ApiResponse<T>> delete<T>(String endpoint) async {
    try {
      final response = await dio.delete(endpoint);
      return ApiResponse.success(response.data as T);
    } on DioException catch (e) {
      return _handleError<T>(e);
    }
  }

  /// Handle API errors with enhanced logging and recovery
  ApiResponse<T> _handleError<T>(DioException error) {
    // Log error for monitoring
    print('API Error: ${error.type} - ${error.message}');
    
    // Update network connectivity status
    final connectivityService = NetworkConnectivityService();
    final errorMessage = error.message ?? 'Unknown error';
    
    if (connectivityService.isNetworkError(errorMessage)) {
      connectivityService.updateBackendStatus(false);
    }
    
    String userMessage;
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.receiveTimeout:
      case DioExceptionType.sendTimeout:
        userMessage = 'Connection timeout. Please try again.';
        break;
      case DioExceptionType.connectionError:
        userMessage = 'No internet connection. Please check your network.';
        break;
      case DioExceptionType.badResponse:
        userMessage = _extractErrorFromResponse(error.response);
        break;
      default:
        userMessage = 'Something went wrong. Please try again.';
    }
    
    return ApiResponse.error(userMessage);
  }

  /// Extract meaningful error message from response
  String _extractErrorFromResponse(Response? response) {
    if (response?.data is Map<String, dynamic>) {
      final data = response!.data as Map<String, dynamic>;
      return data['message'] ?? data['error'] ?? 'Server error';
    }
    return 'Server error (${response?.statusCode})';
  }
}

/// Custom API exceptions
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  
  const ApiException(this.message, [this.statusCode]);
  
  @override
  String toString() => 'ApiException: $message (${statusCode ?? 'Unknown'})';
}

/// Network connectivity exception
class NetworkException extends ApiException {
  const NetworkException(super.message);
}

/// Server error exception
class ServerException extends ApiException {
  const ServerException(super.message, int super.statusCode);
}

/// Authentication exception
class AuthException extends ApiException {
  const AuthException(String message) : super(message, 401);
}
