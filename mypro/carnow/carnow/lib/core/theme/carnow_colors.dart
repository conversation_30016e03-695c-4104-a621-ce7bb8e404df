/// ============================================================================
/// CARNOW COLORS - Material 3 Design System
/// ============================================================================
/// 
/// CarNow brand colors following Material 3 design system
/// Task 6: Create authentication UI components
/// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
/// ============================================================================
library;

import 'package:flutter/material.dart';
import 'app_colors.dart';

/// CarNow brand colors following Material 3 design system
class CarnowColors {
  // =================== CarNow Brand Colors ======================
  // Material 3 compliant color palette for CarNow application
  // Based on existing AppColors but enhanced for Material 3
  // ==========================================================

  /// Primary brand color - CarNow Red
  static const Color primary = AppColors.primary; // Color(0xFFE53238)
  static const Color primaryContainer = Color(0xFFFFDAD6);
  static const Color onPrimary = Colors.white;
  static const Color onPrimaryContainer = Color(0xFF410002);

  /// Secondary brand color - CarNow Yellow
  static const Color secondary = AppColors.secondary; // Color(0xFFF5D400)
  static const Color secondaryContainer = Color(0xFFFFE74C);
  static const Color onSecondary = Color(0xFF1A1A00);
  static const Color onSecondaryContainer = Color(0xFF1F1F00);

  /// Tertiary brand color - CarNow Green
  static const Color tertiary = AppColors.tertiary; // Color(0xFF86B817)
  static const Color tertiaryContainer = Color(0xFFBDE954);
  static const Color onTertiary = Colors.white;
  static const Color onTertiaryContainer = Color(0xFF1A3300);

  /// Error colors
  static const Color error = AppColors.error; // Color(0xFFE53238)
  static const Color errorContainer = Color(0xFFFFDAD6);
  static const Color onError = Colors.white;
  static const Color onErrorContainer = Color(0xFF410002);

  /// Success colors (using tertiary green)
  static const Color success = AppColors.success; // Color(0xFF86B817)
  static const Color successContainer = Color(0xFFBDE954);
  static const Color onSuccess = Colors.white;
  static const Color onSuccessContainer = Color(0xFF1A3300);

  /// Warning colors (using secondary yellow)
  static const Color warning = AppColors.warning; // Color(0xFFF5D400)
  static const Color warningContainer = Color(0xFFFFE74C);
  static const Color onWarning = Color(0xFF1A1A00);
  static const Color onWarningContainer = Color(0xFF1F1F00);

  /// Info colors
  static const Color info = AppColors.info; // Color(0xFF0064D2)
  static const Color infoContainer = Color(0xFFD1E4FF);
  static const Color onInfo = Colors.white;
  static const Color onInfoContainer = Color(0xFF001D36);

  /// Surface colors
  static const Color surface = Colors.white;
  static const Color surfaceVariant = Color(0xFFF3F0F4);
  static const Color onSurface = Color(0xFF1C1B1F);
  static const Color onSurfaceVariant = Color(0xFF49454F);

  /// Background colors
  static const Color background = Color(0xFFFFFBFE);
  static const Color onBackground = Color(0xFF1C1B1F);

  /// Outline colors
  static const Color outline = Color(0xFF79747E);
  static const Color outlineVariant = Color(0xFFCAC4D0);

  /// Shadow and scrim
  static const Color shadow = Color(0xFF000000);
  static const Color scrim = Color(0xFF000000);

  /// Inverse colors
  static const Color inverseSurface = Color(0xFF313033);
  static const Color onInverseSurface = Color(0xFFF4EFF4);
  static const Color inversePrimary = Color(0xFFFFB4AB);

  // =================== Authentication Specific Colors ======================
  
  /// Authentication UI specific colors
  static const Color authBackground = background;
  static const Color authSurface = surface;
  static const Color authPrimary = primary;
  static const Color authSecondary = secondary;
  static const Color authError = error;
  static const Color authSuccess = success;
  static const Color authWarning = warning;

  /// Input field colors
  static const Color inputFill = Color(0xFFF7F2FA);
  static const Color inputBorder = outline;
  static const Color inputFocusedBorder = primary;
  static const Color inputErrorBorder = error;

  /// Button colors
  static const Color buttonPrimary = primary;
  static const Color buttonSecondary = secondary;
  static const Color buttonSurface = surfaceVariant;
  static const Color buttonOutline = outline;

  /// Google button colors (following Google brand guidelines)
  static const Color googleButtonBackground = Colors.white;
  static const Color googleButtonBorder = Color(0xFFDADCE0);
  static const Color googleButtonText = Color(0xFF3C4043);

  // =================== Gradient Colors ======================
  
  /// Primary gradient
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, Color(0xFFFF6F74)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Secondary gradient
  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, Color(0xFFFFE74C)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Success gradient
  static const LinearGradient successGradient = LinearGradient(
    colors: [success, Color(0xFFBDE954)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // =================== Helper Methods ======================
  
  /// Get color with opacity
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }

  /// Get contrasting text color for a given background
  static Color getContrastingTextColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? onSurface : Colors.white;
  }

  /// Get appropriate icon color for a given background
  static Color getIconColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? onSurfaceVariant : Colors.white.withOpacity(0.7);
  }

  /// Get status color based on status type
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'success':
        return success;
      case 'error':
      case 'failed':
        return error;
      case 'warning':
      case 'pending':
        return warning;
      case 'info':
      case 'loading':
        return info;
      default:
        return onSurfaceVariant;
    }
  }

  /// Get authentication state color
  static Color getAuthStateColor(String state) {
    switch (state.toLowerCase()) {
      case 'authenticated':
      case 'success':
        return success;
      case 'error':
      case 'failed':
        return error;
      case 'loading':
      case 'pending':
        return warning;
      case 'unauthenticated':
      case 'initial':
        return onSurfaceVariant;
      default:
        return onSurfaceVariant;
    }
  }
}

/// Material 3 color scheme extension for CarNow
extension CarnowColorScheme on ColorScheme {
  /// Get CarNow branded color scheme
  static ColorScheme get carnowLight => const ColorScheme.light(
    primary: CarnowColors.primary,
    onPrimary: CarnowColors.onPrimary,
    primaryContainer: CarnowColors.primaryContainer,
    onPrimaryContainer: CarnowColors.onPrimaryContainer,
    
    secondary: CarnowColors.secondary,
    onSecondary: CarnowColors.onSecondary,
    secondaryContainer: CarnowColors.secondaryContainer,
    onSecondaryContainer: CarnowColors.onSecondaryContainer,
    
    tertiary: CarnowColors.tertiary,
    onTertiary: CarnowColors.onTertiary,
    tertiaryContainer: CarnowColors.tertiaryContainer,
    onTertiaryContainer: CarnowColors.onTertiaryContainer,
    
    error: CarnowColors.error,
    onError: CarnowColors.onError,
    errorContainer: CarnowColors.errorContainer,
    onErrorContainer: CarnowColors.onErrorContainer,
    
    surface: CarnowColors.surface,
    onSurface: CarnowColors.onSurface,
    surfaceContainerHighest: CarnowColors.surfaceVariant,
    onSurfaceVariant: CarnowColors.onSurfaceVariant,
    
    outline: CarnowColors.outline,
    outlineVariant: CarnowColors.outlineVariant,
    
    shadow: CarnowColors.shadow,
    scrim: CarnowColors.scrim,
    
    inverseSurface: CarnowColors.inverseSurface,
    onInverseSurface: CarnowColors.onInverseSurface,
    inversePrimary: CarnowColors.inversePrimary,
  );

  /// Get CarNow branded dark color scheme
  static ColorScheme get carnowDark => const ColorScheme.dark(
    primary: Color(0xFFFFB4AB),
    onPrimary: Color(0xFF690005),
    primaryContainer: Color(0xFF93000A),
    onPrimaryContainer: Color(0xFFFFDAD6),
    
    secondary: Color(0xFFE6C200),
    onSecondary: Color(0xFF3D3000),
    secondaryContainer: Color(0xFF5A4700),
    onSecondaryContainer: Color(0xFFFFE74C),
    
    tertiary: Color(0xFFA1CD39),
    onTertiary: Color(0xFF2B4700),
    tertiaryContainer: Color(0xFF406800),
    onTertiaryContainer: Color(0xFFBDE954),
    
    error: Color(0xFFFFB4AB),
    onError: Color(0xFF690005),
    errorContainer: Color(0xFF93000A),
    onErrorContainer: Color(0xFFFFDAD6),
    
    surface: Color(0xFF141218),
    onSurface: Color(0xFFE6E0E9),
    surfaceContainerHighest: Color(0xFF49454F),
    onSurfaceVariant: Color(0xFFCAC4D0),
    
    outline: Color(0xFF938F99),
    outlineVariant: Color(0xFF49454F),
    
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    
    inverseSurface: Color(0xFFE6E0E9),
    onInverseSurface: Color(0xFF313033),
    inversePrimary: Color(0xFFE53238),
  );
}
