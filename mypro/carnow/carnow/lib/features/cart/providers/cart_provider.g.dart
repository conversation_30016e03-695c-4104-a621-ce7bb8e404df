// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cart_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$cartRepositoryHash() => r'4dbdd5bc34e57584d57ff0db3da1990e43a020cc';

/// Enhanced Cart Repository Provider
///
/// Copied from [cartRepository].
@ProviderFor(cartRepository)
final cartRepositoryProvider = AutoDisposeProvider<CartRepository>.internal(
  cartRepository,
  name: r'cartRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$cartRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CartRepositoryRef = AutoDisposeProviderRef<CartRepository>;
String _$cartSummaryHash() => r'0f72c9eecf14ab8bd48c54a62fdf709da2c0e0ad';

/// Cart Summary Provider for quick access
///
/// Copied from [cartSummary].
@ProviderFor(cartSummary)
final cartSummaryProvider = AutoDisposeProvider<CartSummaryModel>.internal(
  cartSummary,
  name: r'cartSummaryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$cartSummaryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CartSummaryRef = AutoDisposeProviderRef<CartSummaryModel>;
String _$cartItemCountHash() => r'9d6a97d0788453242e6e33ec69ec9269548e59b0';

/// Cart Item Count Provider for badges and quick display
///
/// Copied from [cartItemCount].
@ProviderFor(cartItemCount)
final cartItemCountProvider = AutoDisposeProvider<int>.internal(
  cartItemCount,
  name: r'cartItemCountProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$cartItemCountHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CartItemCountRef = AutoDisposeProviderRef<int>;
String _$cartTotalHash() => r'6143938f54756897fe2c9cf58d9555450417a608';

/// Cart Total Provider for quick display
///
/// Copied from [cartTotal].
@ProviderFor(cartTotal)
final cartTotalProvider = AutoDisposeProvider<double>.internal(
  cartTotal,
  name: r'cartTotalProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$cartTotalHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CartTotalRef = AutoDisposeProviderRef<double>;
String _$isCartEmptyHash() => r'184006d15bcb5d841bc0e3434ea691d45777ded0';

/// Cart Empty State Provider
///
/// Copied from [isCartEmpty].
@ProviderFor(isCartEmpty)
final isCartEmptyProvider = AutoDisposeProvider<bool>.internal(
  isCartEmpty,
  name: r'isCartEmptyProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isCartEmptyHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsCartEmptyRef = AutoDisposeProviderRef<bool>;
String _$isCartLoadingHash() => r'2c9ddf0f958ce9d4362aab3e583683a4eb94e2b1';

/// Cart Loading State Provider
///
/// Copied from [isCartLoading].
@ProviderFor(isCartLoading)
final isCartLoadingProvider = AutoDisposeProvider<bool>.internal(
  isCartLoading,
  name: r'isCartLoadingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isCartLoadingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsCartLoadingRef = AutoDisposeProviderRef<bool>;
String _$cartHash() => r'0898f01ef701969a1f17c853450892c2c66b9ea1';

/// Cart Provider following Forever Plan architecture
/// Manages the complete cart state with proper error handling and caching
///
/// Copied from [Cart].
@ProviderFor(Cart)
final cartProvider =
    AutoDisposeAsyncNotifierProvider<Cart, CartModel?>.internal(
      Cart.new,
      name: r'cartProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$cartHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$Cart = AutoDisposeAsyncNotifier<CartModel?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
