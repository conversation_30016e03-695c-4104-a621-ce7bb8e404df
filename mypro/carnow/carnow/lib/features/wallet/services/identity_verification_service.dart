import '../models/identity_verification_model.dart';

/// خدمة التحقق من الهوية - DEPRECATED
/// 
/// ⚠️ DEPRECATED: This service violates Forever Plan Architecture
/// Direct Supabase calls are not allowed - use Go Backend API instead
/// 
/// Future Implementation:
/// - POST /api/v1/verification/documents - Upload documents
/// - GET /api/v1/verification/status - Get verification status  
/// - POST /api/v1/verification/submit - Submit verification request
class IdentityVerificationService {
  @Deprecated('Use Go Backend API endpoints for verification operations')
  IdentityVerificationService(dynamic supabase);

  /// الحصول على أنواع المستندات المطلوبة
  @Deprecated('Migrate to GET /api/v1/verification/document-types')
  Future<List<DocumentTypeModel>> getDocumentTypes() async {
    throw UnimplementedError(
      'Identity verification needs migration to Go backend API. '
      'Use GET /api/v1/verification/document-types endpoint instead.'
    );
  }

  /// الحصول على حالات التحقق المتاحة
  @Deprecated('Migrate to GET /api/v1/verification/statuses')
  Future<List<VerificationStatusModel>> getVerificationStatuses() async {
    throw UnimplementedError(
      'Identity verification needs migration to Go backend API. '
      'Use GET /api/v1/verification/statuses endpoint instead.'
    );
  }

  /// الحصول على حالة التحقق الحالية للمستخدم
  @Deprecated('Migrate to GET /api/v1/verification/user')
  Future<List<IdentityVerificationModel>> getMyVerifications() async {
    throw UnimplementedError(
      'Identity verification needs migration to Go backend API. '
      'Use GET /api/v1/verification/user endpoint instead.'
    );
  }

  /// رفع صورة المستند
  @Deprecated('Migrate to POST /api/v1/verification/upload-document')
  Future<String> uploadDocumentImage(
    List<int> fileBytes,
    String fileName,
  ) async {
    throw UnimplementedError(
      'Document upload needs migration to Go backend API. '
      'Use POST /api/v1/verification/upload-document endpoint instead.'
    );
  }

  /// إرسال طلب التحقق من الهوية
  @Deprecated('Migrate to POST /api/v1/verification/submit')
  Future<IdentityVerificationModel> submitVerification({
    required String documentTypeId,
    required String documentNumber,
    required String documentImageUrl,
    DateTime? documentExpiry,
    String? notes,
  }) async {
    throw UnimplementedError(
      'Verification submission needs migration to Go backend API. '
      'Use POST /api/v1/verification/submit endpoint instead.'
    );
  }

  /// التحقق من حالة التحقق الحالية
  /// 🎯 FIXED: Now uses wallet service instead of direct Supabase access
  @Deprecated('Use wallet service verification level instead')
  Future<VerificationLevel> getVerificationLevel() async {
    // Return default verification level since this is deprecated
    return VerificationLevel.basicVerified;
  }

  /// فحص إذا كان المستخدم بحاجة لتحقق أساسي أو كامل
  @Deprecated('Migrate to Go backend verification logic')
  Future<VerificationRequirement> checkVerificationRequirement({
    required bool isSellerAccount,
  }) async {
    // Simplified logic for deprecated service
    if (isSellerAccount) {
      return VerificationRequirement.fullVerification;
    }
    return VerificationRequirement.basicVerification;
  }
}

/// مستويات التحقق
enum VerificationLevel {
  unverified, // غير محقق
  basicVerified, // محقق أساسي
  fullyVerified, // محقق كامل
}

/// متطلبات التحقق
enum VerificationRequirement {
  verified, // محقق
  basicVerification, // يحتاج تحقق أساسي
  fullVerification, // يحتاج تحقق كامل
}

extension VerificationLevelExtension on VerificationLevel {
  String get displayName {
    switch (this) {
      case VerificationLevel.unverified:
        return 'غير محقق';
      case VerificationLevel.basicVerified:
        return 'محقق أساسي';
      case VerificationLevel.fullyVerified:
        return 'محقق كامل';
    }
  }

  String get description {
    switch (this) {
      case VerificationLevel.unverified:
        return 'يجب التحقق من الهوية لاستخدام المحفظة';
      case VerificationLevel.basicVerified:
        return 'يمكن الشحن والتحويل، لكن لا يمكن السحب';
      case VerificationLevel.fullyVerified:
        return 'جميع العمليات المالية متاحة';
    }
  }
}

extension VerificationRequirementExtension on VerificationRequirement {
  String get displayName {
    switch (this) {
      case VerificationRequirement.verified:
        return 'محقق';
      case VerificationRequirement.basicVerification:
        return 'يحتاج تحقق أساسي';
      case VerificationRequirement.fullVerification:
        return 'يحتاج تحقق كامل';
    }
  }

  String get description {
    switch (this) {
      case VerificationRequirement.verified:
        return 'تم التحقق من الهوية بنجاح';
      case VerificationRequirement.basicVerification:
        return 'أرسل صورة الهوية الشخصية للتحقق الأساسي';
      case VerificationRequirement.fullVerification:
        return 'أرسل جميع الوثائق المطلوبة للتحقق الكامل';
    }
  }
}
