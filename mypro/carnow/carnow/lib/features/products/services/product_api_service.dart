import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/config/backend_config.dart';
import '../../../core/networking/simple_api_client.dart';
import '../models/product_model.dart';

part 'product_api_service.g.dart';

/// Service for managing products via Go backend API
/// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
class ProductApiService {
  final SimpleApiClient _apiClient;

  ProductApiService(this._apiClient);

  /// Get all products (simple list)
  Future<List<ProductModel>> getProductsSimple() async {
    try {
      final response = await _apiClient.get(
        CarnowBackendConfig.buildUrlSync('/products-simple'),
      );
      
      if (response.isSuccess && response.data != null) {
        final List<dynamic> data = response.data['data'] ?? response.data;
        return data.map((json) => ProductModel.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load products: ${response.error}');
      }
    } catch (e) {
      throw Exception('Error fetching products: $e');
    }
  }

  /// Get product by ID
  Future<ProductModel> getProductById(String productId) async {
    try {
      final response = await _apiClient.get(
        CarnowBackendConfig.buildUrlSync('/products/$productId'),
      );
      
      if (response.isSuccess && response.data != null) {
        final data = response.data['data'] ?? response.data;
        return ProductModel.fromJson(data);
      } else {
        throw Exception('Failed to load product: ${response.error}');
      }
    } catch (e) {
      throw Exception('Error fetching product: $e');
    }
  }

  /// Get products by user ID
  Future<List<ProductModel>> getProductsByUserId(String userId) async {
    try {
      final response = await _apiClient.get(
        CarnowBackendConfig.buildUrlSync('/users/$userId/products'),
      );
      
      if (response.isSuccess && response.data != null) {
        final List<dynamic> data = response.data['data'] ?? response.data;
        return data.map((json) => ProductModel.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load user products: ${response.error}');
      }
    } catch (e) {
      throw Exception('Error fetching user products: $e');
    }
  }

  /// Create new product
  Future<ProductModel> createProduct(String userId, Map<String, dynamic> productData) async {
    try {
      final response = await _apiClient.post(
        CarnowBackendConfig.buildUrlSync('/users/$userId/products'),
        data: productData,
      );
      
      if (response.isSuccess && response.data != null) {
        final data = response.data['data'] ?? response.data;
        return ProductModel.fromJson(data);
      } else {
        throw Exception('Failed to create product: ${response.error}');
      }
    } catch (e) {
      throw Exception('Error creating product: $e');
    }
  }

  /// Update product
  Future<ProductModel> updateProduct(String productId, Map<String, dynamic> productData) async {
    try {
      final response = await _apiClient.put(
        CarnowBackendConfig.buildUrlSync('/products/$productId'),
        data: productData,
      );
      
      if (response.isSuccess && response.data != null) {
        final data = response.data['data'] ?? response.data;
        return ProductModel.fromJson(data);
      } else {
        throw Exception('Failed to update product: ${response.error}');
      }
    } catch (e) {
      throw Exception('Error updating product: $e');
    }
  }

  /// Delete product
  Future<void> deleteProduct(String productId) async {
    try {
      final response = await _apiClient.delete(
        CarnowBackendConfig.buildUrlSync('/products/$productId'),
      );
      
      if (!response.isSuccess) {
        throw Exception('Failed to delete product: ${response.error}');
      }
    } catch (e) {
      throw Exception('Error deleting product: $e');
    }
  }

  /// Get products by category
  Future<List<ProductModel>> getProductsByCategory(String categoryId) async {
    try {
      final response = await _apiClient.get(
        CarnowBackendConfig.buildUrlSync('/products/category/$categoryId'),
      );
      
      if (response.isSuccess && response.data != null) {
        final List<dynamic> data = response.data['data'] ?? response.data;
        return data.map((json) => ProductModel.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load category products: ${response.error}');
      }
    } catch (e) {
      throw Exception('Error fetching category products: $e');
    }
  }

  /// Search products
  Future<List<ProductModel>> searchProducts(String query, {Map<String, dynamic>? filters}) async {
    try {
      final queryParams = <String, dynamic>{'q': query};
      if (filters != null) {
        queryParams.addAll(filters);
      }
      
      final response = await _apiClient.get(
        CarnowBackendConfig.buildUrlSync('/products/search'),
        queryParameters: queryParams,
      );
      
      if (response.isSuccess && response.data != null) {
        final List<dynamic> data = response.data['data'] ?? response.data;
        return data.map((json) => ProductModel.fromJson(json)).toList();
      } else {
        throw Exception('Failed to search products: ${response.error}');
      }
    } catch (e) {
      throw Exception('Error searching products: $e');
    }
  }
}

/// Provider for ProductApiService
@riverpod
ProductApiService productApiService(Ref ref) {
  final apiClient = ref.watch(simpleApiClientProvider);
  return ProductApiService(apiClient);
}