import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'package:carnow/features/taxonomy/models/subcategory_model.dart';
import '../widgets/product_card.dart';
import '../widgets/dynamic_filter_sidebar.dart';
import '../providers/spec_filter_provider.dart';
import '../../services/product_api_service.dart';

class SubcategoryProductsScreen extends HookConsumerWidget {
  const SubcategoryProductsScreen({super.key, required this.subCategory});

  final SubCategoryModel subCategory;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filterMap = ref.watch(specFilterProvider);

    final productsAsync = ref.watch(
      _productsProvider((subCategory.id, filterMap)),
    );

    return Scaffold(
      appBar: AppBar(title: Text(subCategory.nameEn)),
      endDrawer: DynamicFilterSidebar(subCategory: subCategory),
      body: productsAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (err, st) => Center(child: Text('Error: $err')),
        data: (products) => GridView.builder(
          padding: const EdgeInsets.all(16),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            mainAxisSpacing: 12,
            crossAxisSpacing: 12,
            childAspectRatio: 0.66,
          ),
          itemCount: products.length,
          itemBuilder: (context, index) =>
              ProductCard(product: products[index]),
        ),
      ),
    );
  }
}

final _productsProvider = FutureProvider.autoDispose.family((
  ref,
  (String subcategoryId, Map<String, dynamic> specFilters) args,
) async {
  final apiService = ref.watch(productApiServiceProvider);
  final products = await apiService.getProductsSimple();
  
  // Filter by subcategory locally
  final filteredProducts = products.where((product) => 
    product.subcategoryId == args.$1
  ).toList();
  
  // Apply spec filters if any
  if (args.$2.isNotEmpty) {
    // TODO: Implement spec filtering logic
    // For now, just return the subcategory filtered products
  }
  
  return filteredProducts.take(50).toList();
});
