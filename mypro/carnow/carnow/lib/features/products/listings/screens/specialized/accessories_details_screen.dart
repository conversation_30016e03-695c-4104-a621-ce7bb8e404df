import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

import '../../../../../core/models/enums.dart';
import '../../../../../core/utils/formatters.dart';
import '../../../../../core/models/product_model.dart';
import '../../../../seller/providers/seller_profile_provider.dart';
import '../../../../cart/providers/cart_provider.dart';

/// شاشة تفاصيل الإكسسوارات المتخصصة
class AccessoriesDetailsScreen extends HookConsumerWidget {
  const AccessoriesDetailsScreen({
    super.key,
    required this.product,
    required this.isOwner,
  });

  final ProductModel product;
  final bool isOwner;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final pageController = usePageController();

    return Scaffold(
      backgroundColor: Colors.white,
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(context, pageController),
          SliverToBoxAdapter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildBasicInfo(context),
                const SizedBox(height: 16),
                _buildAccessoryFeatures(context),
                const SizedBox(height: 16),
                _buildInstallationInfo(context),
                const SizedBox(height: 16),
                _buildSellerInfo(context, ref),
                const SizedBox(height: 100),
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomActions(context, ref),
    );
  }

  Widget _buildSliverAppBar(
    BuildContext context,
    PageController pageController,
  ) {
    return SliverAppBar(
      expandedHeight: 300,
      pinned: true,
      backgroundColor: Colors.white,
      foregroundColor: Colors.black,
      elevation: 0,
      leading: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => context.pop(),
        ),
      ),
      actions: [
        Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            icon: const Icon(Icons.share, color: Colors.black),
            onPressed: () {},
          ),
        ),
        if (!isOwner)
          Container(
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: IconButton(
              icon: const Icon(Icons.favorite_border, color: Colors.black),
              onPressed: () {},
            ),
          ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: _buildImageCarousel(pageController),
      ),
    );
  }

  Widget _buildImageCarousel(PageController pageController) {
    final images = product.images;

    if (images.isEmpty) {
      return Container(
        color: Colors.grey[100],
        child: const Center(
          child: Icon(Icons.category, size: 64, color: Colors.grey),
        ),
      );
    }

    return Stack(
      children: [
        PageView.builder(
          controller: pageController,
          itemCount: images.length,
          itemBuilder: (context, index) {
            return Container(
              color: Colors.white,
              child: CachedNetworkImage(
                imageUrl: images[index],
                fit: BoxFit.contain,
                placeholder: (context, url) => Container(
                  color: Colors.grey[100],
                  child: const Center(child: CircularProgressIndicator()),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey[100],
                  child: const Icon(Icons.error_outline, size: 64),
                ),
              ),
            );
          },
        ),

        if (images.length > 1)
          Positioned(
            bottom: 16,
            left: 0,
            right: 0,
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: SmoothPageIndicator(
                  controller: pageController,
                  count: images.length,
                  effect: const WormEffect(
                    dotHeight: 8,
                    dotWidth: 8,
                    activeDotColor: Colors.white,
                    dotColor: Colors.white54,
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildBasicInfo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            product.nameAr ?? product.name,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),

          const SizedBox(height: 8),

          Row(
            children: [
              Text(
                Formatters.formatCurrency(product.price),
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(width: 8),
              const Text(
                'د.ل',
                style: TextStyle(fontSize: 18, color: Colors.black),
              ),
            ],
          ),

          const SizedBox(height: 12),
          _buildConditionBadge(),
          const SizedBox(height: 12),
          _buildQuickInfo(),
        ],
      ),
    );
  }

  Widget _buildConditionBadge() {
    String label;
    Color color;

    switch (product.condition) {
      case ProductCondition.new_:
        label = 'جديد';
        color = Colors.green;
        break;
      case ProductCondition.likeNew:
        label = 'كالجديد';
        color = Colors.lightGreen;
        break;
      case ProductCondition.good:
        label = 'جيد';
        color = Colors.blue;
        break;
      case ProductCondition.used:
        label = 'مستعمل';
        color = Colors.orange;
        break;
      case ProductCondition.refurbished:
        label = 'مجدد';
        color = Colors.teal;
        break;
      default:
        label = 'غير محدد';
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        border: Border.all(color: color),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildQuickInfo() {
    return Column(
      children: [
        if (product.brand != null) _buildInfoRow('الماركة', product.brand!),
        if (product.model != null) _buildInfoRow('الموديل', product.model!),
        _buildInfoRow('الكمية المتاحة', '\${product.stockQuantity}'),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(
            width: 100,
            child: Text(
              '\$label:',
              style: TextStyle(color: Colors.grey, fontSize: 14),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.black,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccessoryFeatures(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'مميزات المنتج',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 12),

          if (product.description != null) ...[
            Text(product.description!, style: const TextStyle(fontSize: 14)),
            const SizedBox(height: 12),
          ],

          if (product.specifications != null &&
              product.specifications!.isNotEmpty) ...[
            const Text(
              'المواصفات',
              style: TextStyle(fontWeight: FontWeight.w600, color: Colors.grey),
            ),
            const SizedBox(height: 8),
            ...product.specifications!.entries.map(
              (entry) => _buildInfoRow(entry.key, entry.value.toString()),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInstallationInfo(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.purple[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.purple[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.build_circle, color: Colors.purple[600], size: 20),
              const SizedBox(width: 8),
              Text(
                'معلومات التركيب',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.purple[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text('• سهل التركيب', style: TextStyle(fontSize: 14)),
          const Text('• لا يحتاج أدوات خاصة', style: TextStyle(fontSize: 14)),
          const Text('• دليل التركيب مرفق', style: TextStyle(fontSize: 14)),
          const Text('• خدمة تركيب متاحة', style: TextStyle(fontSize: 14)),
        ],
      ),
    );
  }

  Widget _buildSellerInfo(BuildContext context, WidgetRef ref) {
    final sellerId = product.sellerId;
    final sellerAsync = ref.watch(sellerProfileProvider(sellerId));

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'معلومات البائع',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 12),

          sellerAsync.when(
            data: _buildSellerCard,
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => _buildSellerCard(null),
          ),
        ],
      ),
    );
  }

  Widget _buildSellerCard(dynamic seller) {
    String displayName = 'بائع غير معروف';
    if (seller != null && seller.name != null) {
      displayName = seller.name;
    }

    return Row(
      children: [
        CircleAvatar(
          radius: 25,
          backgroundColor: Colors.purple[100],
          child: const Icon(Icons.person, color: Colors.purple),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                displayName,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const Text(
                '100% تقييم إيجابي',
                style: TextStyle(color: Colors.green, fontSize: 12),
              ),
              const Text(
                'متخصص في الإكسسوارات',
                style: TextStyle(color: Colors.purple, fontSize: 12),
              ),
            ],
          ),
        ),
        TextButton(onPressed: () {}, child: const Text('عرض الملف الشخصي')),
      ],
    );
  }

  Widget _buildBottomActions(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isOwner) ...[
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: () {},
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'تعديل الإعلان',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ] else ...[
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: () {},
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'شراء الآن',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ),

              const SizedBox(height: 12),

              SizedBox(
                width: double.infinity,
                height: 48,
                child: OutlinedButton(
                  onPressed: () async {
                    try {
                      await ref.read(cartProvider.notifier).addItem(product.id, 1);
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('تمت الإضافة للسلة بنجاح'),
                          ),
                        );
                      }
                    } catch (e) {
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('تعذر إضافة المنتج للسلة'),
                          ),
                        );
                      }
                    }
                  },
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: Colors.purple),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'إضافة للسلة',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.purple,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
