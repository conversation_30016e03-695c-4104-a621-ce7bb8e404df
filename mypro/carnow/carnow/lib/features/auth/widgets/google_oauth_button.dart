/// ============================================================================
/// GOOGLE OAUTH BUTTON - Forever Plan Architecture
/// ============================================================================
///
/// Google OAuth button widget that uses Go Backend for authentication
/// NO direct Supabase calls from Flutter - only through Go API
/// ============================================================================
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../core/auth/unified_auth_provider.dart';
import '../../../core/auth/auth_models.dart';
import '../../../core/config/backend_config.dart';

/// Google OAuth button widget
class GoogleOAuthButton extends ConsumerWidget {
  const GoogleOAuthButton({
    super.key,
    this.onSuccess,
    this.onError,
    this.isLoading = false,
  });

  final VoidCallback? onSuccess;
  final Function(String error)? onError;
  final bool isLoading;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(unifiedAuthProviderProvider);
    final logger = Logger('GoogleOAuthButton');

    return SizedBox(
      width: double.infinity,
      height: 48,
      child: ElevatedButton.icon(
        onPressed: isLoading || authState is AuthStateLoading
            ? null
            : () => _handleGoogleSignIn(ref, logger),
        icon: _buildGoogleIcon(),
        label: _buildButtonText(authState),
        style: _buildButtonStyle(context),
      ),
    );
  }

  Widget _buildGoogleIcon() {
    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
      ),
      child: const Icon(Icons.g_mobiledata, color: Colors.red, size: 20),
    );
  }

  Widget _buildButtonText(AuthState authState) {
    if (authState is AuthStateLoading) {
      return const SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    }

    return const Text(
      'تسجيل الدخول بـ Google',
      style: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: Colors.white,
      ),
    );
  }

  ButtonStyle _buildButtonStyle(BuildContext context) {
    return ElevatedButton.styleFrom(
      backgroundColor: const Color(0xFF4285F4), // Google Blue
      foregroundColor: Colors.white,
      disabledBackgroundColor: const Color(0xFF4285F4).withValues(alpha: 0.6),
      disabledForegroundColor: Colors.white.withValues(alpha: 0.6),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 2,
      shadowColor: const Color(0xFF4285F4).withValues(alpha: 0.3),
    );
  }

  Future<void> _handleGoogleSignIn(WidgetRef ref, Logger logger) async {
    try {
      logger.info('Initiating Google OAuth sign in');

      // Debug: Check the URL being used
      logger.info(
        '🔗 GoogleOAuthButton: URL = ${CarnowBackendConfig.googleOAuthUrl}',
      );
      logger.info('🔧 GoogleOAuthButton: Starting authentication process...');

      final authProvider = ref.read(unifiedAuthProviderProvider.notifier);
      logger.info(
        '🔧 GoogleOAuthButton: Auth provider obtained, calling signInWithGoogle()...',
      );
      final result = await authProvider.signInWithGoogle();

      // Check the result
      if (result is AuthResultSuccess) {
        logger.info('Google OAuth sign in successful');
        onSuccess?.call();
      } else if (result is AuthResultFailure) {
        logger.warning('Google OAuth sign in failed: ${result.error}');
        onError?.call(result.error);
      }
    } catch (e) {
      logger.severe('Google OAuth sign in error: $e');
      onError?.call('حدث خطأ أثناء تسجيل الدخول بـ Google');
    }
  }
}

/// Google OAuth button with custom styling
class CustomGoogleOAuthButton extends ConsumerWidget {
  const CustomGoogleOAuthButton({
    super.key,
    this.onSuccess,
    this.onError,
    this.isLoading = false,
    this.style,
    this.textStyle,
    this.iconSize = 24,
    this.height = 48,
    this.borderRadius = 12,
  });

  final VoidCallback? onSuccess;
  final Function(String error)? onError;
  final bool isLoading;
  final ButtonStyle? style;
  final TextStyle? textStyle;
  final double iconSize;
  final double height;
  final double borderRadius;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(unifiedAuthProviderProvider);
    final logger = Logger('CustomGoogleOAuthButton');

    return SizedBox(
      width: double.infinity,
      height: height,
      child: ElevatedButton.icon(
        onPressed: isLoading || authState is AuthStateLoading
            ? null
            : () => _handleGoogleSignIn(ref, logger),
        icon: _buildCustomGoogleIcon(),
        label: _buildCustomButtonText(authState),
        style: style ?? _buildCustomButtonStyle(context),
      ),
    );
  }

  Widget _buildCustomGoogleIcon() {
    return Container(
      width: iconSize,
      height: iconSize,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Icon(Icons.g_mobiledata, color: Colors.red, size: iconSize * 0.8),
    );
  }

  Widget _buildCustomButtonText(AuthState authState) {
    if (authState is AuthStateLoading) {
      return SizedBox(
        width: iconSize,
        height: iconSize,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            textStyle?.color ?? Colors.white,
          ),
        ),
      );
    }

    return Text(
      'تسجيل الدخول بـ Google',
      style:
          textStyle ??
          const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.white,
          ),
    );
  }

  ButtonStyle _buildCustomButtonStyle(BuildContext context) {
    return ElevatedButton.styleFrom(
      backgroundColor: const Color(0xFF4285F4),
      foregroundColor: Colors.white,
      disabledBackgroundColor: const Color(0xFF4285F4).withValues(alpha: 0.6),
      disabledForegroundColor: Colors.white.withValues(alpha: 0.6),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      elevation: 2,
      shadowColor: const Color(0xFF4285F4).withValues(alpha: 0.3),
    );
  }

  Future<void> _handleGoogleSignIn(WidgetRef ref, Logger logger) async {
    try {
      logger.info('Initiating custom Google OAuth sign in');

      final authProvider = ref.read(unifiedAuthProviderProvider.notifier);
      final result = await authProvider.signInWithGoogle();

      // Check the result
      if (result is AuthResultSuccess) {
        logger.info('Custom Google OAuth sign in successful');
        onSuccess?.call();
      } else if (result is AuthResultFailure) {
        logger.warning('Custom Google OAuth sign in failed: ${result.error}');
        onError?.call(result.error);
      }
    } catch (e) {
      logger.severe('Custom Google OAuth sign in error: $e');
      onError?.call('حدث خطأ أثناء تسجيل الدخول بـ Google');
    }
  }
}
