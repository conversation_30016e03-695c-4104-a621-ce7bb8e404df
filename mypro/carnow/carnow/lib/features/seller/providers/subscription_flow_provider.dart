import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../core/error/app_error_factory.dart';
import '../../../core/auth/unified_auth_provider.dart';
import '../../../core/models/subscription_request.dart';
import '../../../core/models/subscription_error.dart';

import '../models/subscription_model.dart';
import '../models/seller_enums.dart';
import '../services/subscription_service.dart';


part 'subscription_flow_provider.g.dart';

final _logger = Logger('SubscriptionFlowProvider');

/// Subscription Flow State Management - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// ✅ Uses SubscriptionService for all business logic
/// ✅ NO direct Supabase calls
/// ✅ Real data only from Go backend
/// ✅ Material 3 compliant state management
/// ✅ Bilingual error handling (Arabic/English)

// =============================================================================
// FORM STATE MODELS
// =============================================================================

/// Subscription form data with validation
class SubscriptionFormData {
  final SubscriptionPlan? selectedPlan;
  final BillingCycle? selectedBillingCycle;
  final String? paymentMethodId;
  final Map<String, dynamic>? businessDocuments;
  final bool isValid;
  final Map<String, String> validationErrors;
  final Map<String, String> validationErrorsAr;

  const SubscriptionFormData({
    this.selectedPlan,
    this.selectedBillingCycle,
    this.paymentMethodId,
    this.businessDocuments,
    this.isValid = false,
    this.validationErrors = const {},
    this.validationErrorsAr = const {},
  });

  SubscriptionFormData copyWith({
    SubscriptionPlan? selectedPlan,
    BillingCycle? selectedBillingCycle,
    String? paymentMethodId,
    Map<String, dynamic>? businessDocuments,
    bool? isValid,
    Map<String, String>? validationErrors,
    Map<String, String>? validationErrorsAr,
  }) {
    return SubscriptionFormData(
      selectedPlan: selectedPlan ?? this.selectedPlan,
      selectedBillingCycle: selectedBillingCycle ?? this.selectedBillingCycle,
      paymentMethodId: paymentMethodId ?? this.paymentMethodId,
      businessDocuments: businessDocuments ?? this.businessDocuments,
      isValid: isValid ?? this.isValid,
      validationErrors: validationErrors ?? this.validationErrors,
      validationErrorsAr: validationErrorsAr ?? this.validationErrorsAr,
    );
  }
}

/// Subscription flow status with real-time updates
enum SubscriptionFlowStatus {
  initial,
  selectingPlan,
  configuringPayment,
  submitting,
  processing,
  completed,
  failed,
  cancelled,
}

/// Subscription flow state
class SubscriptionFlowState {
  final SubscriptionFlowStatus status;
  final SubscriptionFormData formData;
  final String? currentStepId;
  final double progress;
  final String? successMessage;
  final String? successMessageAr;
  final String? errorMessage;
  final String? errorMessageAr;
  final bool canNavigateBack;
  final bool canNavigateForward;
  final DateTime lastUpdated;

  const SubscriptionFlowState({
    this.status = SubscriptionFlowStatus.initial,
    this.formData = const SubscriptionFormData(),
    this.currentStepId,
    this.progress = 0.0,
    this.successMessage,
    this.successMessageAr,
    this.errorMessage,
    this.errorMessageAr,
    this.canNavigateBack = false,
    this.canNavigateForward = false,
    required this.lastUpdated,
  });

  SubscriptionFlowState copyWith({
    SubscriptionFlowStatus? status,
    SubscriptionFormData? formData,
    String? currentStepId,
    double? progress,
    String? successMessage,
    String? successMessageAr,
    String? errorMessage,
    String? errorMessageAr,
    bool? canNavigateBack,
    bool? canNavigateForward,
    DateTime? lastUpdated,
  }) {
    return SubscriptionFlowState(
      status: status ?? this.status,
      formData: formData ?? this.formData,
      currentStepId: currentStepId ?? this.currentStepId,
      progress: progress ?? this.progress,
      successMessage: successMessage ?? this.successMessage,
      successMessageAr: successMessageAr ?? this.successMessageAr,
      errorMessage: errorMessage ?? this.errorMessage,
      errorMessageAr: errorMessageAr ?? this.errorMessageAr,
      canNavigateBack: canNavigateBack ?? this.canNavigateBack,
      canNavigateForward: canNavigateForward ?? this.canNavigateForward,
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }

  bool get isLoading => status == SubscriptionFlowStatus.submitting || 
                       status == SubscriptionFlowStatus.processing;
  
  bool get hasError => errorMessage != null;
  
  bool get isCompleted => status == SubscriptionFlowStatus.completed;
  
  bool get canSubmit => formData.isValid && !isLoading;
}

// =============================================================================
// SUBSCRIPTION FLOW PROVIDER
// =============================================================================

/// Main subscription flow provider with comprehensive state management
@riverpod
class SubscriptionFlowProvider extends _$SubscriptionFlowProvider {
  @override
  SubscriptionFlowState build() {
    return SubscriptionFlowState(
      lastUpdated: DateTime.now(),
    );
  }

  /// Initialize subscription flow
  Future<void> initializeFlow() async {
    try {
      _logger.info('Initializing subscription flow');
      
      final user = ref.read(currentUserProvider);
      if (user == null) {
        throw AppErrorFactory.invalidCredentials();
      }

      state = state.copyWith(
        status: SubscriptionFlowStatus.selectingPlan,
        currentStepId: 'plan_selection',
        progress: 0.1,
        canNavigateBack: false,
        canNavigateForward: false,
        lastUpdated: DateTime.now(),
      );

      _logger.info('Subscription flow initialized successfully');
    } catch (e, st) {
      _logger.severe('Error initializing subscription flow', e, st);
      _setError(
        'Failed to initialize subscription flow',
        'فشل في تهيئة عملية الاشتراك',
      );
    }
  }

  /// Update selected subscription plan with validation
  Future<void> updateSelectedPlan(SubscriptionPlan plan) async {
    try {
      _logger.info('Updating selected plan: ${plan.id}');

      final updatedFormData = state.formData.copyWith(
        selectedPlan: plan,
      );

      final validatedFormData = await _validateFormData(updatedFormData);

      state = state.copyWith(
        formData: validatedFormData,
        progress: 0.3,
        canNavigateForward: validatedFormData.isValid,
        lastUpdated: DateTime.now(),
      );

      _logger.info('Selected plan updated successfully: ${plan.tier}');
    } catch (e, st) {
      _logger.severe('Error updating selected plan', e, st);
      _setError(
        'Failed to update subscription plan',
        'فشل في تحديث خطة الاشتراك',
      );
    }
  }

  /// Update billing cycle with validation
  Future<void> updateBillingCycle(BillingCycle billingCycle) async {
    try {
      _logger.info('Updating billing cycle: ${billingCycle.name}');

      final updatedFormData = state.formData.copyWith(
        selectedBillingCycle: billingCycle,
      );

      final validatedFormData = await _validateFormData(updatedFormData);

      state = state.copyWith(
        formData: validatedFormData,
        progress: 0.5,
        canNavigateForward: validatedFormData.isValid,
        lastUpdated: DateTime.now(),
      );

      _logger.info('Billing cycle updated successfully: ${billingCycle.name}');
    } catch (e, st) {
      _logger.severe('Error updating billing cycle', e, st);
      _setError(
        'Failed to update billing cycle',
        'فشل في تحديث دورة الفوترة',
      );
    }
  }

  /// Update payment method with validation
  Future<void> updatePaymentMethod(String paymentMethodId) async {
    try {
      _logger.info('Updating payment method: $paymentMethodId');

      final updatedFormData = state.formData.copyWith(
        paymentMethodId: paymentMethodId,
      );

      final validatedFormData = await _validateFormData(updatedFormData);

      state = state.copyWith(
        formData: validatedFormData,
        status: SubscriptionFlowStatus.configuringPayment,
        currentStepId: 'payment_configuration',
        progress: 0.7,
        canNavigateBack: true,
        canNavigateForward: validatedFormData.isValid,
        lastUpdated: DateTime.now(),
      );

      _logger.info('Payment method updated successfully');
    } catch (e, st) {
      _logger.severe('Error updating payment method', e, st);
      _setError(
        'Failed to update payment method',
        'فشل في تحديث طريقة الدفع',
      );
    }
  }

  /// Submit subscription request through SubscriptionService
  Future<void> submitSubscription() async {
    if (!state.canSubmit) {
      _logger.warning('Cannot submit subscription - form not valid or already submitting');
      return;
    }

    try {
      _logger.info('Submitting subscription request');

      state = state.copyWith(
        status: SubscriptionFlowStatus.submitting,
        progress: 0.8,
        canNavigateBack: false,
        canNavigateForward: false,
        errorMessage: null,
        errorMessageAr: null,
        lastUpdated: DateTime.now(),
      );

      final subscriptionService = ref.read(subscriptionServiceImplProvider);
      final formData = state.formData;

      // Prepare subscription request
      // Note: Store details should be collected in a previous step or from user profile
      final request = SubscriptionRequest(
        storeName: 'Store Name', // This should come from form data or user profile
        phone: '**********', // This should come from form data or user profile
        city: 'Riyadh', // This should come from form data or user profile
        address: 'Store Address', // This should come from form data or user profile
        description: 'Store Description', // This should come from form data or user profile
        planType: formData.selectedPlan?.name ?? 'basic',
        price: formData.selectedPlan?.monthlyPriceLD ?? 0.0,
        userId: ref.read(currentUserProvider)?.id ?? '',
      );

      // Call SubscriptionService to handle business logic
      final result = await subscriptionService.submitSubscriptionRequest(request);

      state = state.copyWith(
        status: SubscriptionFlowStatus.processing,
        progress: 0.9,
        lastUpdated: DateTime.now(),
      );

      // Simulate processing time for better UX
      await Future.delayed(const Duration(seconds: 1));

      state = state.copyWith(
        status: SubscriptionFlowStatus.completed,
        progress: 1.0,
        successMessage: 'Subscription created successfully!',
        successMessageAr: 'تم إنشاء الاشتراك بنجاح!',
        canNavigateForward: true,
        lastUpdated: DateTime.now(),
      );

      String? requestId;
      result.when(
        success: (response) {
          requestId = response.id;
          _logger.info('Subscription submitted successfully: $requestId');
        },
        failure: (error) {
          // Handle error
          _logger.severe('Error submitting subscription: ${error.technicalMessage}');
        },
      );
      
      // Only log success if we have a request ID
      if (requestId != null) {
        _logger.info('Subscription submitted successfully: $requestId');
      }

      // Navigate to success screen after short delay
      Future.delayed(const Duration(seconds: 2), () {
        if (requestId != null) {
          _navigateToSuccess(requestId!);
        }
      });

    } catch (e, st) {
      _logger.severe('Error submitting subscription', e, st);
      
      state = state.copyWith(
        status: SubscriptionFlowStatus.failed,
        canNavigateBack: true,
        canNavigateForward: false,
        lastUpdated: DateTime.now(),
      );

      _setError(
        'Failed to create subscription. Please try again.',
        'فشل في إنشاء الاشتراك. يرجى المحاولة مرة أخرى.',
      );
    }
  }

  /// Cancel subscription flow
  Future<void> cancelFlow() async {
    try {
      _logger.info('Cancelling subscription flow');

      state = state.copyWith(
        status: SubscriptionFlowStatus.cancelled,
        progress: 0.0,
        errorMessage: null,
        errorMessageAr: null,
        successMessage: null,
        successMessageAr: null,
        lastUpdated: DateTime.now(),
      );

      _logger.info('Subscription flow cancelled successfully');
    } catch (e, st) {
      _logger.severe('Error cancelling subscription flow', e, st);
    }
  }

  /// Reset flow to initial state
  Future<void> resetFlow() async {
    try {
      _logger.info('Resetting subscription flow');

      state = SubscriptionFlowState(
        lastUpdated: DateTime.now(),
      );

      _logger.info('Subscription flow reset successfully');
    } catch (e, st) {
      _logger.severe('Error resetting subscription flow', e, st);
    }
  }

  /// Retry failed submission
  Future<void> retrySubmission() async {
    if (state.status != SubscriptionFlowStatus.failed) {
      _logger.warning('Cannot retry - flow not in failed state');
      return;
    }

    _logger.info('Retrying subscription submission');
    await submitSubscription();
  }

  // =============================================================================
  // PRIVATE HELPER METHODS
  // =============================================================================

  /// Validate form data with bilingual error messages
  Future<SubscriptionFormData> _validateFormData(SubscriptionFormData formData) async {
    final errors = <String, String>{};
    final errorsAr = <String, String>{};

    // Validate selected plan
    if (formData.selectedPlan == null) {
      errors['plan'] = 'Please select a subscription plan';
      errorsAr['plan'] = 'يرجى اختيار خطة اشتراك';
    }

    // Validate billing cycle
    if (formData.selectedBillingCycle == null) {
      errors['billing_cycle'] = 'Please select a billing cycle';
      errorsAr['billing_cycle'] = 'يرجى اختيار دورة الفوترة';
    }

    // Validate payment method for paid plans
    if (formData.selectedPlan != null && 
        formData.selectedPlan!.tier != SubscriptionTier.starter &&
        (formData.paymentMethodId == null || formData.paymentMethodId!.isEmpty)) {
      errors['payment_method'] = 'Payment method is required for paid plans';
      errorsAr['payment_method'] = 'طريقة الدفع مطلوبة للخطط المدفوعة';
    }

    final isValid = errors.isEmpty;

    return formData.copyWith(
      isValid: isValid,
      validationErrors: errors,
      validationErrorsAr: errorsAr,
    );
  }

  /// Set error state with bilingual messages
  void _setError(String message, String messageAr) {
    state = state.copyWith(
      errorMessage: message,
      errorMessageAr: messageAr,
      lastUpdated: DateTime.now(),
    );
  }

  /// Navigate to success screen using SubscriptionNavigationManager
  Future<void> _navigateToSuccess(String subscriptionId) async {
    try {
      // This would be called from the UI layer with proper BuildContext
      _logger.info('Navigation to success screen requested for subscription: $subscriptionId');
    } catch (e, st) {
      _logger.severe('Error navigating to success screen', e, st);
    }
  }
}

// =============================================================================
// ADDITIONAL PROVIDERS FOR SUBSCRIPTION FLOW
// =============================================================================

/// Provider for current form validation state
@riverpod
bool isSubscriptionFormValid(Ref ref) {
  final flowState = ref.watch(subscriptionFlowProviderProvider);
  return flowState.formData.isValid;
}

/// Provider for current step progress
@riverpod
double subscriptionProgress(Ref ref) {
  final flowState = ref.watch(subscriptionFlowProviderProvider);
  return flowState.progress;
}

/// Provider for loading state
@riverpod
bool isSubscriptionLoading(Ref ref) {
  final flowState = ref.watch(subscriptionFlowProviderProvider);
  return flowState.isLoading;
}

/// Provider for error state
@riverpod
String? subscriptionError(Ref ref) {
  final flowState = ref.watch(subscriptionFlowProviderProvider);
  return flowState.errorMessage;
}

/// Provider for Arabic error state
@riverpod
String? subscriptionErrorAr(Ref ref) {
  final flowState = ref.watch(subscriptionFlowProviderProvider);
  return flowState.errorMessageAr;
}

/// Provider for success state
@riverpod
String? subscriptionSuccess(Ref ref) {
  final flowState = ref.watch(subscriptionFlowProviderProvider);
  return flowState.successMessage;
}

/// Provider for Arabic success state
@riverpod
String? subscriptionSuccessAr(Ref ref) {
  final flowState = ref.watch(subscriptionFlowProviderProvider);
  return flowState.successMessageAr;
}
