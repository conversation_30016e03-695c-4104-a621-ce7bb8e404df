import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/auth/unified_auth_provider.dart';
import '../../products/services/product_api_service.dart';

import '../models/seller_dashboard_stats_model.dart';
import 'seller_stats_provider.dart';

part 'seller_dashboard_provider.g.dart';

final _logger = Logger('SellerDashboardProvider');

@riverpod
class SellerDashboard extends _$SellerDashboard {
  @override
  Future<SellerDashboardStatsModel> build() async {
    _logger.info('Building seller dashboard stats');

    // Get base stats from seller stats provider
    final statsAsync = await ref.watch(sellerDashboardStatsProvider.future);

    // Get current user ID
    final currentUser = ref.watch(currentUserProvider);
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }
    final userId = currentUser.id;

    // Get top selling products (this would require order data in a real implementation)
    final apiService = ref.read(productApiServiceProvider);
    final products = await apiService.getProductsByUserId(userId);
    
    // Filter available products and take top 5
    final availableProducts = products
        .where((product) => product.isAvailable)
        .take(5)
        .toList();

    // For now, we're just showing most recently added products
    // In a real implementation, this would show products with most sales

    // Convert to dashboard stats model
    return SellerDashboardStatsModel(
      totalProducts: statsAsync['totalProducts'] ?? 0,
      activeProducts: statsAsync['activeListings'] ?? 0,
      totalOrders: statsAsync['totalOrders'] ?? 0,
      topSellingProducts: availableProducts,
      recentProducts: [], // This would come from recent products data
    );
  }

  Future<void> refresh() async {
    ref.invalidateSelf();
    ref.invalidate(sellerDashboardStatsProvider);
  }
}
