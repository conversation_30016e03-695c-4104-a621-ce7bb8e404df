import 'package:carnow/core/models/api_response.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../models/store_model.dart';
import '../../../core/networking/simple_api_client.dart';

part 'store_repository.g.dart';

/// Store repository for managing seller stores
@riverpod
StoreRepository storeRepository(Ref ref) =>
    StoreRepository(apiClient: ref.read(simpleApiClientProvider));

class StoreRepository {
  StoreRepository({required SimpleApiClient apiClient}) : _apiClient = apiClient;
  final SimpleApiClient _apiClient;

  /// Get all store categories
  Future<List<StoreCategoryModel>> getStoreCategories() async {
    try {
      final response = await _apiClient.get('/stores/categories');

      if (response.isSuccess && response.data != null) {
        return (response.data as List)
            .map(
              (json) => StoreCategoryModel.fromJson(json as Map<String, dynamic>),
            )
            .toList();
      } else {
        throw Exception('Failed to fetch store categories: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to fetch store categories: $e');
    }
  }

  /// Get store by seller ID
  Future<StoreModel?> getStoreBySellerID(int sellerId) async {
    try {
      final response = await _apiClient.get('/stores/seller/$sellerId');

      if (response.isSuccess && response.data != null) {
        return StoreModel.fromJson(response.data);
      } else if (response.isSuccess && response.data == null) {
        return null;
      } else {
        throw Exception('Failed to fetch store: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to fetch store: $e');
    }
  }

  /// Get store by slug
  Future<StoreModel?> getStoreBySlug(String slug) async {
    try {
      final response = await _apiClient.get('/stores/slug/$slug');

      if (response.isSuccess && response.data != null) {
        return StoreModel.fromJson(response.data);
      } else if (response.isSuccess && response.data == null) {
        return null;
      } else {
        throw Exception('Failed to fetch store by slug: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to fetch store by slug: $e');
    }
  }

  /// Get featured stores
  Future<List<StoreModel>> getFeaturedStores({int limit = 10}) async {
    try {
      final response = await _apiClient.get(
        '/stores/featured',
        queryParameters: {'limit': limit},
      );

      if (response.isSuccess && response.data != null) {
        return (response.data as List)
            .map((json) => StoreModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception('Failed to fetch featured stores: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to fetch featured stores: $e');
    }
  }

  /// Get stores by category
  Future<List<StoreModel>> getStoresByCategory(
    int categoryId, {
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiClient.get(
        '/stores/category/$categoryId',
        queryParameters: {'page': page, 'limit': limit},
      );

      if (response.isSuccess && response.data != null) {
        return (response.data as List)
            .map((json) => StoreModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception('Failed to fetch stores by category: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to fetch stores by category: $e');
    }
  }

  /// Search stores
  Future<List<StoreModel>> searchStores(
    String query, {
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiClient.get(
        '/stores/search',
        queryParameters: {'query': query, 'page': page, 'limit': limit},
      );

      if (response.isSuccess && response.data != null) {
        return (response.data as List)
            .map((json) => StoreModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception('Failed to search stores: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to search stores: $e');
    }
  }

  /// Create or update store
  Future<StoreModel> createOrUpdateStore(StoreModel store) async {
    try {
      final storeData = store.toJson();
      storeData.remove('id'); // Remove ID for insert
      storeData.remove('created_at');
      storeData.remove('updated_at');
      storeData['updated_at'] = DateTime.now().toIso8601String();

      final response = await _apiClient.post('/stores', data: storeData);

      if (response.isSuccess && response.data != null) {
        return StoreModel.fromJson(response.data);
      } else {
        throw Exception('Failed to create/update store: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to create/update store: $e');
    }
  }

  /// Add store section
  Future<StoreSectionModel> addStoreSection(StoreSectionModel section) async {
    try {
      final sectionData = section.toJson();
      sectionData.remove('id');
      sectionData.remove('created_at');
      sectionData.remove('updated_at');

      final response = await _apiClient.post(
        '/stores/sections',
        data: sectionData,
      );

      if (response.isSuccess && response.data != null) {
        return StoreSectionModel.fromJson(response.data);
      } else {
        throw Exception('Failed to add store section: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to add store section: $e');
    }
  }

  /// Get store sections
  Future<List<StoreSectionModel>> getStoreSections(int storeId) async {
    try {
      final response = await _apiClient.get('/stores/$storeId/sections');

      if (response.isSuccess && response.data != null) {
        return (response.data as List)
            .map(
              (json) => StoreSectionModel.fromJson(json as Map<String, dynamic>),
            )
            .toList();
      } else {
        throw Exception('Failed to fetch store sections: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to fetch store sections: $e');
    }
  }

  /// Follow/Unfollow store
  Future<void> toggleStoreFollow(int storeId, bool isFollowing) async {
    try {
      late final ApiResponse response;
      if (isFollowing) {
        response = await _apiClient.delete('/stores/$storeId/follow');
      } else {
        response = await _apiClient.post('/stores/$storeId/follow');
      }
      
      if (!response.isSuccess) {
        throw Exception('Failed to toggle store follow: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to toggle store follow: $e');
    }
  }

  /// Get store statistics
  Future<StoreStatsModel> getStoreStats(int storeId) async {
    try {
      final response = await _apiClient.get('/stores/$storeId/stats');

      if (response.isSuccess && response.data != null) {
        return StoreStatsModel.fromJson(response.data);
      } else {
        throw Exception('Failed to fetch store statistics: ${response.error}');
      }
    } catch (e) {
      throw Exception('Failed to fetch store statistics: $e');
    }
  }
}
