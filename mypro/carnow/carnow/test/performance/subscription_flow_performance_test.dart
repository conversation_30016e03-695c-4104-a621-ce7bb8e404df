import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import 'package:carnow/core/providers/subscription_flow_provider.dart';
import 'package:carnow/core/services/subscription_service.dart';
import 'package:carnow/core/models/subscription_request.dart';
import 'package:carnow/core/models/subscription_response.dart';
import 'package:carnow/core/models/subscription_error.dart';
import 'package:carnow/core/auth/unified_auth_provider.dart';
import 'package:carnow/core/auth/auth_models.dart';

// Performance-focused mock service
class PerformanceMockSubscriptionService implements SubscriptionService {
  final Duration responseDelay;
  final bool shouldFail;
  final int callCount = 0;

  PerformanceMockSubscriptionService({
    this.responseDelay = const Duration(milliseconds: 100),
    this.shouldFail = false,
  });

  @override
  Future<SubscriptionResult<SubscriptionResponse>> submitSubscriptionRequest(
    SubscriptionRequest request,
  ) async {
    await Future.delayed(responseDelay);

    if (shouldFail) {
      return const SubscriptionResult.failure(
        SubscriptionError.networkError(
          message: 'Performance test network error',
          code: 'PERF_TEST_ERROR',
        ),
      );
    }

    return SubscriptionResult.success(
      SubscriptionResponse(
        id: 'perf-test-${DateTime.now().millisecondsSinceEpoch}',
        status: 'success',
        createdAt: DateTime.now(),
        message: 'Performance test subscription created',
      ),
    );
  }

  @override
  Future<SubscriptionResult<List<SubscriptionResponse>>> getUserSubscriptions(
    String userId,
  ) async {
    await Future.delayed(responseDelay);

    return SubscriptionResult.success([
      SubscriptionResponse(
        id: 'perf-test-1',
        status: 'active',
        createdAt: DateTime.now(),
        message: 'Performance test subscription 1',
      ),
      SubscriptionResponse(
        id: 'perf-test-2',
        status: 'pending',
        createdAt: DateTime.now(),
        message: 'Performance test subscription 2',
      ),
    ]);
  }

  @override
  Future<SubscriptionResult<SubscriptionResponse>> getSubscriptionStatus(
    String subscriptionId,
  ) async {
    await Future.delayed(responseDelay);

    return SubscriptionResult.success(
      SubscriptionResponse(
        id: subscriptionId,
        status: 'active',
        createdAt: DateTime.now(),
        message: 'Performance test subscription status',
      ),
    );
  }

  @override
  Future<SubscriptionResult<SubscriptionResponse>> updateSubscription(
    String subscriptionId,
    SubscriptionRequest request,
  ) async {
    await Future.delayed(responseDelay);

    return SubscriptionResult.success(
      SubscriptionResponse(
        id: subscriptionId,
        status: 'updated',
        createdAt: DateTime.now(),
        message: 'Performance test subscription updated',
      ),
    );
  }

  @override
  Future<SubscriptionResult<SubscriptionResponse>> cancelSubscription(
    String subscriptionId,
  ) async {
    await Future.delayed(responseDelay);

    return SubscriptionResult.success(
      SubscriptionResponse(
        id: subscriptionId,
        status: 'cancelled',
        createdAt: DateTime.now(),
        message: 'Performance test subscription cancelled',
      ),
    );
  }
}

void main() {
  group('Subscription Flow Performance Tests', () {
    late ProviderContainer container;
    late PerformanceMockSubscriptionService mockService;

    setUp(() {
      // Configure logging for performance monitoring
      Logger.root.level = Level.INFO;
      Logger.root.onRecord.listen((record) {
        // Monitor log performance
        if (record.level >= Level.WARNING) {
          print('${record.level.name}: ${record.time}: ${record.message}');
        }
      });

      mockService = PerformanceMockSubscriptionService();

      container = ProviderContainer(
        overrides: [
          subscriptionServiceProvider.overrideWithValue(mockService),
          currentUserProvider.overrideWith(
            (ref) => User(
              id: '550e8400-e29b-41d4-a716-446655440000',
              email: '<EMAIL>',
              firstName: 'Performance',
              lastName: 'Test',
              phoneNumber: '+966501234567',
              isActive: true,
              emailVerified: true,
              createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
              updatedAt: DateTime.parse('2024-01-01T00:00:00Z'),
            ),
          ),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('Form Validation Performance', () {
      test('should validate form fields quickly', () async {
        final notifier = container.read(
          coreSubscriptionFlowProviderProvider.notifier,
        );

        await notifier.initializeFlow();

        final stopwatch = Stopwatch()..start();

        // Test rapid form updates
        for (int i = 0; i < 100; i++) {
          await notifier.updateFormField(
            storeName: 'Performance Test Store $i',
            phone: '+96650123456${i % 10}',
            city: 'Performance City $i',
            address:
                'Performance address with sufficient length for validation $i',
            description:
                'Performance description with sufficient length for validation $i',
            planType: 'basic',
            price: 100.0 + i,
          );
        }

        stopwatch.stop();

        // Should complete 100 validations within 2 seconds
        expect(stopwatch.elapsedMilliseconds, lessThan(2000));
        print(
          'Form validation performance: ${stopwatch.elapsedMilliseconds}ms for 100 updates',
        );

        final finalState = container.read(coreSubscriptionFlowProviderProvider);
        expect(finalState.formData.isValid, true);
        expect(finalState.formData.storeName, 'Performance Test Store 99');
      });

      test('should handle complex validation scenarios efficiently', () async {
        final notifier = container.read(
          coreSubscriptionFlowProviderProvider.notifier,
        );

        await notifier.initializeFlow();

        final stopwatch = Stopwatch()..start();

        // Test validation with various invalid inputs
        final testCases = [
          // Invalid cases
          {
            'storeName': 'A',
            'phone': 'invalid',
            'city': '',
            'address': 'short',
            'description': '',
            'planType': 'invalid',
            'price': -1.0,
          },
          {
            'storeName': '',
            'phone': '+966501234567',
            'city': 'Valid',
            'address': 'Valid address',
            'description': 'Valid description',
            'planType': 'basic',
            'price': 200.0,
          },
          // Valid cases
          {
            'storeName': 'Valid Store',
            'phone': '+966501234567',
            'city': 'Valid City',
            'address': 'Valid address with sufficient length',
            'description': 'Valid description with sufficient length',
            'planType': 'basic',
            'price': 200.0,
          },
        ];

        for (int i = 0; i < 50; i++) {
          final testCase = testCases[i % testCases.length];
          await notifier.updateFormField(
            storeName: testCase['storeName'] as String,
            phone: testCase['phone'] as String,
            city: testCase['city'] as String,
            address: testCase['address'] as String,
            description: testCase['description'] as String,
            planType: testCase['planType'] as String,
            price: testCase['price'] as double,
          );
        }

        stopwatch.stop();

        // Should handle complex validation scenarios within 3 seconds
        expect(stopwatch.elapsedMilliseconds, lessThan(3000));
        print(
          'Complex validation performance: ${stopwatch.elapsedMilliseconds}ms for 50 complex validations',
        );
      });
    });

    group('Submission Performance', () {
      test('should submit subscription within acceptable time', () async {
        mockService = PerformanceMockSubscriptionService(
          responseDelay: const Duration(
            milliseconds: 500,
          ), // Simulate realistic API delay
        );

        container = ProviderContainer(
          overrides: [
            subscriptionServiceProvider.overrideWithValue(mockService),
            currentUserProvider.overrideWith(
              (ref) => User(
                id: '550e8400-e29b-41d4-a716-446655440000',
                email: '<EMAIL>',
                firstName: 'Performance',
                lastName: 'Test',
                phoneNumber: '+966501234567',
                isActive: true,
                emailVerified: true,
                createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
                updatedAt: DateTime.parse('2024-01-01T00:00:00Z'),
              ),
            ),
          ],
        );

        final notifier = container.read(
          coreSubscriptionFlowProviderProvider.notifier,
        );

        await notifier.initializeFlow();
        await notifier.updateFormField(
          storeName: 'Performance Test Store',
          phone: '+966501234567',
          city: 'Performance City',
          address: 'Performance address with sufficient length for validation',
          description:
              'Performance description with sufficient length for validation',
          planType: 'basic',
          price: 200.0,
        );

        final stopwatch = Stopwatch()..start();

        await notifier.submitSubscription();

        stopwatch.stop();

        // Should complete submission within 2 seconds (including API delay)
        expect(stopwatch.elapsedMilliseconds, lessThan(2000));
        print(
          'Subscription submission performance: ${stopwatch.elapsedMilliseconds}ms',
        );

        final finalState = container.read(coreSubscriptionFlowProviderProvider);
        expect(finalState.status, CoreSubscriptionFlowStatus.completed);

        container.dispose();
      });

      test('should handle multiple concurrent submissions efficiently', () async {
        final containers = <ProviderContainer>[];
        final futures = <Future<void>>[];

        final stopwatch = Stopwatch()..start();

        // Create multiple containers for concurrent testing
        for (int i = 0; i < 5; i++) {
          final testContainer = ProviderContainer(
            overrides: [
              subscriptionServiceProvider.overrideWithValue(
                PerformanceMockSubscriptionService(
                  responseDelay: const Duration(milliseconds: 200),
                ),
              ),
              currentUserProvider.overrideWith(
                (ref) => User(
                  id: '550e8400-e29b-41d4-a716-44665544000$i',
                  email: 'perf$<EMAIL>',
                  firstName: 'Performance',
                  lastName: 'Test$i',
                  phoneNumber: '+96650123456$i',
                  isActive: true,
                  emailVerified: true,
                  createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
                  updatedAt: DateTime.parse('2024-01-01T00:00:00Z'),
                ),
              ),
            ],
          );

          containers.add(testContainer);

          final notifier = testContainer.read(
            coreSubscriptionFlowProviderProvider.notifier,
          );

          futures.add(() async {
            await notifier.initializeFlow();
            await notifier.updateFormField(
              storeName: 'Concurrent Test Store $i',
              phone: '+96650123456$i',
              city: 'Concurrent City $i',
              address:
                  'Concurrent address with sufficient length for validation $i',
              description:
                  'Concurrent description with sufficient length for validation $i',
              planType: 'basic',
              price: 200.0 + i,
            );
            await notifier.submitSubscription();
          }());
        }

        await Future.wait(futures);

        stopwatch.stop();

        // Should handle 5 concurrent submissions within 1 second (due to parallelism)
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
        print(
          'Concurrent submissions performance: ${stopwatch.elapsedMilliseconds}ms for 5 concurrent submissions',
        );

        // Verify all submissions completed successfully
        for (final testContainer in containers) {
          final state = testContainer.read(
            coreSubscriptionFlowProviderProvider,
          );
          expect(state.status, CoreSubscriptionFlowStatus.completed);
          testContainer.dispose();
        }
      });
    });

    group('Error Handling Performance', () {
      test('should handle errors quickly without blocking UI', () async {
        mockService = PerformanceMockSubscriptionService(
          responseDelay: const Duration(milliseconds: 100),
          shouldFail: true,
        );

        container = ProviderContainer(
          overrides: [
            subscriptionServiceProvider.overrideWithValue(mockService),
            currentUserProvider.overrideWith(
              (ref) => User(
                id: '550e8400-e29b-41d4-a716-446655440000',
                email: '<EMAIL>',
                firstName: 'Performance',
                lastName: 'Test',
                phoneNumber: '+966501234567',
                isActive: true,
                emailVerified: true,
                createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
                updatedAt: DateTime.parse('2024-01-01T00:00:00Z'),
              ),
            ),
          ],
        );

        final notifier = container.read(
          coreSubscriptionFlowProviderProvider.notifier,
        );

        await notifier.initializeFlow();
        await notifier.updateFormField(
          storeName: 'Error Test Store',
          phone: '+966501234567',
          city: 'Error City',
          address: 'Error address with sufficient length for validation',
          description:
              'Error description with sufficient length for validation',
          planType: 'basic',
          price: 200.0,
        );

        final stopwatch = Stopwatch()..start();

        await notifier.submitSubscription();

        stopwatch.stop();

        // Should handle error quickly
        expect(stopwatch.elapsedMilliseconds, lessThan(500));
        print('Error handling performance: ${stopwatch.elapsedMilliseconds}ms');

        final finalState = container.read(coreSubscriptionFlowProviderProvider);
        expect(finalState.status, CoreSubscriptionFlowStatus.failed);
        expect(finalState.hasError, true);

        container.dispose();
      });

      test('should handle rapid error recovery efficiently', () async {
        final failingService = PerformanceMockSubscriptionService(
          responseDelay: const Duration(milliseconds: 50),
          shouldFail: true,
        );

        final successService = PerformanceMockSubscriptionService(
          responseDelay: const Duration(milliseconds: 50),
          shouldFail: false,
        );

        container = ProviderContainer(
          overrides: [
            subscriptionServiceProvider.overrideWithValue(failingService),
            currentUserProvider.overrideWith(
              (ref) => User(
                id: '550e8400-e29b-41d4-a716-446655440000',
                email: '<EMAIL>',
                firstName: 'Performance',
                lastName: 'Test',
                phoneNumber: '+966501234567',
                isActive: true,
                emailVerified: true,
                createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
                updatedAt: DateTime.parse('2024-01-01T00:00:00Z'),
              ),
            ),
          ],
        );

        final notifier = container.read(
          coreSubscriptionFlowProviderProvider.notifier,
        );

        await notifier.initializeFlow();
        await notifier.updateFormField(
          storeName: 'Recovery Test Store',
          phone: '+966501234567',
          city: 'Recovery City',
          address: 'Recovery address with sufficient length for validation',
          description:
              'Recovery description with sufficient length for validation',
          planType: 'basic',
          price: 200.0,
        );

        final stopwatch = Stopwatch()..start();

        // First submission fails
        await notifier.submitSubscription();
        expect(
          container.read(coreSubscriptionFlowProviderProvider).status,
          CoreSubscriptionFlowStatus.failed,
        );

        // Switch to success service for retry
        container = ProviderContainer(
          overrides: [
            subscriptionServiceProvider.overrideWithValue(successService),
            currentUserProvider.overrideWith(
              (ref) => User(
                id: '550e8400-e29b-41d4-a716-446655440000',
                email: '<EMAIL>',
                firstName: 'Performance',
                lastName: 'Test',
                phoneNumber: '+966501234567',
                isActive: true,
                emailVerified: true,
                createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
                updatedAt: DateTime.parse('2024-01-01T00:00:00Z'),
              ),
            ),
          ],
        );

        final retryNotifier = container.read(
          coreSubscriptionFlowProviderProvider.notifier,
        );

        await retryNotifier.initializeFlow();
        await retryNotifier.updateFormField(
          storeName: 'Recovery Test Store',
          phone: '+966501234567',
          city: 'Recovery City',
          address: 'Recovery address with sufficient length for validation',
          description:
              'Recovery description with sufficient length for validation',
          planType: 'basic',
          price: 200.0,
        );

        // Retry succeeds
        await retryNotifier.submitSubscription();

        stopwatch.stop();

        // Should handle error and recovery within 1 second
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
        print('Error recovery performance: ${stopwatch.elapsedMilliseconds}ms');

        final finalState = container.read(coreSubscriptionFlowProviderProvider);
        expect(finalState.status, CoreSubscriptionFlowStatus.completed);

        container.dispose();
      });
    });

    group('Memory Performance', () {
      test('should not leak memory during rapid operations', () async {
        final containers = <ProviderContainer>[];

        // Create and dispose many containers to test memory leaks
        for (int i = 0; i < 20; i++) {
          final testContainer = ProviderContainer(
            overrides: [
              subscriptionServiceProvider.overrideWithValue(
                PerformanceMockSubscriptionService(
                  responseDelay: const Duration(milliseconds: 10),
                ),
              ),
              currentUserProvider.overrideWith(
                (ref) => User(
                  id: '550e8400-e29b-41d4-a716-44665544000$i',
                  email: 'memory$<EMAIL>',
                  firstName: 'Memory',
                  lastName: 'Test$i',
                  phoneNumber: '+96650123456$i',
                  isActive: true,
                  emailVerified: true,
                  createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
                  updatedAt: DateTime.parse('2024-01-01T00:00:00Z'),
                ),
              ),
            ],
          );

          final notifier = testContainer.read(
            coreSubscriptionFlowProviderProvider.notifier,
          );

          await notifier.initializeFlow();
          await notifier.updateFormField(
            storeName: 'Memory Test Store $i',
            phone: '+96650123456$i',
            city: 'Memory City $i',
            address: 'Memory address with sufficient length for validation $i',
            description:
                'Memory description with sufficient length for validation $i',
            planType: 'basic',
            price: 200.0 + i,
          );

          containers.add(testContainer);
        }

        // Dispose all containers
        for (final testContainer in containers) {
          testContainer.dispose();
        }

        // Test should complete without memory issues
        expect(containers.length, 20);
        print('Memory test completed: Created and disposed 20 containers');
      });

      test('should handle large form data efficiently', () async {
        final notifier = container.read(
          coreSubscriptionFlowProviderProvider.notifier,
        );

        await notifier.initializeFlow();

        final stopwatch = Stopwatch()..start();

        // Test with large strings
        final largeString = 'A' * 1000; // 1KB string

        await notifier.updateFormField(
          storeName: 'Large Data Test Store',
          phone: '+966501234567',
          city: 'Large Data City',
          address: 'Large address: $largeString',
          description: 'Large description: $largeString',
          planType: 'basic',
          price: 200.0,
        );

        stopwatch.stop();

        // Should handle large data within reasonable time
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
        print(
          'Large data handling performance: ${stopwatch.elapsedMilliseconds}ms',
        );

        final finalState = container.read(coreSubscriptionFlowProviderProvider);
        expect(finalState.formData.address.length, greaterThan(1000));
        expect(finalState.formData.description.length, greaterThan(1000));
      });
    });

    group('State Management Performance', () {
      test('should update state efficiently', () async {
        final notifier = container.read(
          coreSubscriptionFlowProviderProvider.notifier,
        );

        await notifier.initializeFlow();

        final stopwatch = Stopwatch()..start();

        // Perform many state updates
        for (int i = 0; i < 50; i++) {
          await notifier.updateFormField(
            storeName: 'State Test Store $i',
            price: 100.0 + i,
          );
        }

        stopwatch.stop();

        // Should update state efficiently
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
        print(
          'State update performance: ${stopwatch.elapsedMilliseconds}ms for 50 updates',
        );

        final finalState = container.read(coreSubscriptionFlowProviderProvider);
        expect(finalState.formData.storeName, 'State Test Store 49');
        expect(finalState.formData.price, 149.0);
      });

      test('should handle provider rebuilds efficiently', () async {
        final stopwatch = Stopwatch()..start();

        // Read multiple providers rapidly
        for (int i = 0; i < 100; i++) {
          container.read(isCoreSubscriptionFormValidProvider);
          container.read(coreSubscriptionProgressProvider);
          container.read(isCoreSubscriptionLoadingProvider);
          container.read(coreSubscriptionErrorProvider);
          container.read(coreSubscriptionFlowStatusProvider);
        }

        stopwatch.stop();

        // Should handle provider reads efficiently
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
        print(
          'Provider rebuild performance: ${stopwatch.elapsedMilliseconds}ms for 500 provider reads',
        );
      });
    });
  });
}
