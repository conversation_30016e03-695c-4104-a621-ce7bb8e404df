// =============================================================================
// CARNOW EMAIL AUTHENTICATION SERVICE TESTS
// =============================================================================
// 
// Comprehensive unit tests for EmailAuthService covering:
// - Email validation with various formats and edge cases
// - Password strength validation with security rules
// - Form validation for sign-in and sign-up
// - Input sanitization and error handling
// - Arabic error messages and localization
//
// Test Coverage:
// - Email Validation Tests (8 tests)
// - Password Validation Tests (10 tests)
// - Name Validation Tests (6 tests)
// - Form Validation Tests (4 tests)
// - Utility Methods Tests (4 tests)
//
// Author: CarNow Development Team
// =============================================================================

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:carnow/core/auth/email_auth_service.dart';
import 'package:carnow/core/auth/auth_models.dart';

void main() {
  group('EmailAuthService', () {
    late ProviderContainer container;
    late EmailAuthService emailAuthService;

    setUp(() {
      container = ProviderContainer();
      emailAuthService = container.read(emailAuthServiceProvider.notifier);
    });

    tearDown(() {
      container.dispose();
    });

    // =========================================================================
    // EMAIL VALIDATION TESTS
    // =========================================================================

    group('Email Validation', () {
      test('should validate correct email formats', () {
        final validEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          'arabic.user@موقع.com',
        ];

        for (final email in validEmails) {
          final result = emailAuthService.validateEmail(email);
          expect(result.isValid, true, reason: 'Email $email should be valid');
          expect(result.email, email.trim().toLowerCase());
        }
      });

      test('should reject invalid email formats', () {
        final invalidEmails = [
          '',
          'invalid-email',
          '@domain.com',
          'user@',
          '<EMAIL>',
          'user@domain',
          '<EMAIL>',
          '<EMAIL>',
          'user <EMAIL>',
        ];

        for (final email in invalidEmails) {
          final result = emailAuthService.validateEmail(email);
          expect(result.isValid, false, reason: 'Email $email should be invalid');
          expect(result.errorMessage, isNotNull);
        }
      });

      test('should reject disposable email domains', () {
        final disposableEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        for (final email in disposableEmails) {
          final result = emailAuthService.validateEmail(email);
          expect(result.isValid, false);
          expect(result.errorMessage, contains('مؤقت'));
        }
      });

      test('should handle email length limits', () {
        // Too long email
        final longEmail = '${'a' * 250}@example.com';
        final longResult = emailAuthService.validateEmail(longEmail);
        expect(longResult.isValid, false);
        expect(longResult.errorMessage, contains('طويل'));
      });

      test('should normalize email case and whitespace', () {
        final result = emailAuthService.validateEmail('  <EMAIL>  ');
        expect(result.isValid, true);
        expect(result.email, '<EMAIL>');
      });

      test('should validate email domain format', () {
        final invalidDomains = [
          '<EMAIL>',
          'user@example.',
          '<EMAIL>',
        ];

        for (final email in invalidDomains) {
          final result = emailAuthService.validateEmail(email);
          expect(result.isValid, false);
          expect(result.errorMessage, contains('نطاق'));
        }
      });

      test('should handle empty email input', () {
        final result = emailAuthService.validateEmail('');
        expect(result.isValid, false);
        expect(result.errorType, EmailValidationError.empty);
        expect(result.errorMessage, contains('مطلوب'));
      });

      test('should validate complex email formats', () {
        final complexEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        for (final email in complexEmails) {
          final result = emailAuthService.validateEmail(email);
          expect(result.isValid, true, reason: 'Complex email $email should be valid');
        }
      });
    });

    // =========================================================================
    // PASSWORD VALIDATION TESTS
    // =========================================================================

    group('Password Validation', () {
      test('should validate strong passwords', () {
        final strongPasswords = [
          'MyStr0ng!Pass',
          'C0mpl3x@Password123',
          'Secure#Pass2024!',
          'V3ry\$tr0ng&P@ssw0rd',
        ];

        for (final password in strongPasswords) {
          final result = emailAuthService.validatePassword(password);
          expect(result.isValid, true, reason: 'Password $password should be valid');
          expect(result.strength, greaterThan(60));
        }
      });

      test('should reject weak passwords', () {
        final weakPasswords = [
          '',
          '123',
          'password',
          '12345678',
          'abcdefgh',
          'PASSWORD',
          'Pass123',
        ];

        for (final password in weakPasswords) {
          final result = emailAuthService.validatePassword(password);
          expect(result.isValid, false, reason: 'Password $password should be invalid');
          expect(result.errors, isNotEmpty);
          expect(result.suggestions, isNotEmpty);
        }
      });

      test('should enforce minimum length requirement', () {
        final result = emailAuthService.validatePassword('Sh0rt!');
        expect(result.isValid, false);
        expect(result.errors, contains(PasswordValidationError.tooShort));
        expect(result.suggestions.any((s) => s.contains('8')), true);
      });

      test('should require character variety', () {
        // No lowercase
        var result = emailAuthService.validatePassword('PASSWORD123!');
        expect(result.errors, contains(PasswordValidationError.noLowercase));

        // No uppercase
        result = emailAuthService.validatePassword('password123!');
        expect(result.errors, contains(PasswordValidationError.noUppercase));

        // No digits
        result = emailAuthService.validatePassword('Password!');
        expect(result.errors, contains(PasswordValidationError.noDigits));

        // No special characters
        result = emailAuthService.validatePassword('Password123');
        expect(result.errors, contains(PasswordValidationError.noSpecialChars));
      });

      test('should detect common passwords', () {
        final commonPasswords = [
          'password',
          '123456',
          'qwerty',
          'admin',
          'password123',
        ];

        for (final password in commonPasswords) {
          final result = emailAuthService.validatePassword(password);
          expect(result.errors, contains(PasswordValidationError.tooCommon));
        }
      });

      test('should detect sequential characters', () {
        final sequentialPasswords = [
          'Password123!',
          'MyPass456@',
          'Test789#',
          'Abcd123!',
        ];

        for (final password in sequentialPasswords) {
          final result = emailAuthService.validatePassword(password);
          expect(result.errors, contains(PasswordValidationError.sequential));
        }
      });

      test('should detect repeated characters', () {
        final repeatedPasswords = [
          'Passsword123!',
          'MyPass111@',
          'Testaaa#',
        ];

        for (final password in repeatedPasswords) {
          final result = emailAuthService.validatePassword(password);
          expect(result.errors, contains(PasswordValidationError.repeated));
        }
      });

      test('should calculate password strength correctly', () {
        // Very weak password
        var result = emailAuthService.validatePassword('123');
        expect(result.strength, lessThan(20));

        // Weak password
        result = emailAuthService.validatePassword('password');
        expect(result.strength, lessThan(40));

        // Medium password
        result = emailAuthService.validatePassword('Password123');
        expect(result.strength, greaterThan(40));
        expect(result.strength, lessThan(80));

        // Strong password
        result = emailAuthService.validatePassword('MyStr0ng!Pass');
        expect(result.strength, greaterThan(60));
      });

      test('should provide strength descriptions', () {
        var result = emailAuthService.validatePassword('MyStr0ng!Pass2024');
        expect(result.strengthDescription, anyOf(['قوية', 'قوية جداً']));

        result = emailAuthService.validatePassword('weak');
        expect(result.strengthDescription, anyOf(['ضعيفة', 'ضعيفة جداً']));
      });

      test('should validate password confirmation', () {
        expect(emailAuthService.validatePasswordConfirmation('password', 'password'), true);
        expect(emailAuthService.validatePasswordConfirmation('password', 'different'), false);
        expect(emailAuthService.validatePasswordConfirmation('', 'password'), false);
        expect(emailAuthService.validatePasswordConfirmation('password', ''), false);
      });
    });

    // =========================================================================
    // NAME VALIDATION TESTS
    // =========================================================================

    group('Name Validation', () {
      test('should validate correct first names', () {
        final validNames = [
          'أحمد',
          'محمد',
          'Ahmed',
          'Mohammed',
          'Jean-Pierre',
          "O'Connor",
          'Mary Jane',
        ];

        for (final name in validNames) {
          final result = emailAuthService.validateFirstName(name);
          expect(result, isNull, reason: 'Name $name should be valid');
        }
      });

      test('should reject invalid first names', () {
        final invalidNames = [
          '',
          'A',
          'A' * 51, // Too long
          'Ahmed123',
          'Ahmed@',
          'Ahmed#',
        ];

        for (final name in invalidNames) {
          final result = emailAuthService.validateFirstName(name);
          expect(result, isNotNull, reason: 'Name $name should be invalid');
        }
      });

      test('should validate correct last names', () {
        final validNames = [
          'العلي',
          'الأحمد',
          'Smith',
          'Johnson',
          'Van Der Berg',
          "O'Neill",
        ];

        for (final name in validNames) {
          final result = emailAuthService.validateLastName(name);
          expect(result, isNull, reason: 'Last name $name should be valid');
        }
      });

      test('should reject invalid last names', () {
        final invalidNames = [
          '',
          'S',
          'S' * 51, // Too long
          'Smith123',
          'Smith@',
          'Smith#',
        ];

        for (final name in invalidNames) {
          final result = emailAuthService.validateLastName(name);
          expect(result, isNotNull, reason: 'Last name $name should be invalid');
        }
      });

      test('should handle Arabic names correctly', () {
        expect(emailAuthService.validateFirstName('محمد'), isNull);
        expect(emailAuthService.validateLastName('العلي'), isNull);
        expect(emailAuthService.validateFirstName('عبد الرحمن'), isNull);
        expect(emailAuthService.validateLastName('بن سعد'), isNull);
      });

      test('should handle special characters in names', () {
        expect(emailAuthService.validateFirstName("O'Connor"), isNull);
        expect(emailAuthService.validateLastName('Jean-Pierre'), isNull);
        expect(emailAuthService.validateFirstName('Mary Jane'), isNull);
      });
    });

    // =========================================================================
    // FORM VALIDATION TESTS
    // =========================================================================

    group('Form Validation', () {
      test('should validate complete sign-in form', () {
        final result = emailAuthService.validateSignInForm(
          email: '<EMAIL>',
          password: 'password123',
        );

        expect(result.isValid, true);
        expect(result.errors, isEmpty);
        expect(result.validatedEmail, '<EMAIL>');
      });

      test('should reject invalid sign-in form', () {
        final result = emailAuthService.validateSignInForm(
          email: 'invalid-email',
          password: '',
        );

        expect(result.isValid, false);
        expect(result.errors, hasLength(2));
        expect(result.errors['email'], isNotNull);
        expect(result.errors['password'], isNotNull);
      });

      test('should validate complete sign-up form', () {
        final result = emailAuthService.validateSignUpForm(
          firstName: 'أحمد',
          lastName: 'العلي',
          email: '<EMAIL>',
          password: 'MyStr0ng!Pass',
          passwordConfirmation: 'MyStr0ng!Pass',
        );

        expect(result.isValid, true);
        expect(result.errors, isEmpty);
        expect(result.validatedEmail, '<EMAIL>');
        expect(result.validatedFirstName, 'أحمد');
        expect(result.validatedLastName, 'العلي');
        expect(result.passwordStrength, greaterThan(60));
      });

      test('should reject invalid sign-up form', () {
        final result = emailAuthService.validateSignUpForm(
          firstName: '',
          lastName: 'S',
          email: 'invalid-email',
          password: 'weak',
          passwordConfirmation: 'different',
        );

        expect(result.isValid, false);
        expect(result.errors, hasLength(5));
        expect(result.errors['firstName'], contains('مطلوب'));
        expect(result.errors['lastName'], contains('قصير'));
        expect(result.errors['email'], contains('تنسيق'));
        expect(result.errors['password'], isNotNull);
        expect(result.errors['passwordConfirmation'], contains('متطابقة'));
      });
    });

    // =========================================================================
    // UTILITY METHODS TESTS
    // =========================================================================

    group('Utility Methods', () {
      test('should sanitize email input', () {
        expect(emailAuthService.sanitizeEmail('  <EMAIL>  '), '<EMAIL>');
        expect(emailAuthService.sanitizeEmail('<EMAIL>'), '<EMAIL>');
      });

      test('should sanitize name input', () {
        expect(emailAuthService.sanitizeName('  Ahmed   Ali  '), 'Ahmed Ali');
        expect(emailAuthService.sanitizeName('محمد    العلي'), 'محمد العلي');
      });

      test('should provide Arabic error messages', () {
        final messages = [
          emailAuthService.getAuthErrorMessage(AuthErrorType.invalidCredentials),
          emailAuthService.getAuthErrorMessage(AuthErrorType.userNotFound),
          emailAuthService.getAuthErrorMessage(AuthErrorType.emailAlreadyExists),
          emailAuthService.getAuthErrorMessage(AuthErrorType.networkError),
        ];

        for (final message in messages) {
          expect(message, isNotEmpty);
          expect(message, isNot(contains('null')));
        }
      });

      test('should handle all auth error types', () {
        for (final errorType in AuthErrorType.values) {
          final message = emailAuthService.getAuthErrorMessage(errorType);
          expect(message, isNotEmpty);
          expect(message, isA<String>());
        }
      });
    });

    // =========================================================================
    // EDGE CASES AND ERROR HANDLING
    // =========================================================================

    group('Edge Cases', () {
      test('should handle null and empty inputs gracefully', () {
        expect(emailAuthService.validateEmail('').isValid, false);
        expect(emailAuthService.validatePassword('').isValid, false);
        expect(emailAuthService.validateFirstName(''), isNotNull);
        expect(emailAuthService.validateLastName(''), isNotNull);
      });

      test('should handle very long inputs', () {
        final longString = 'a' * 1000;
        
        expect(emailAuthService.validateEmail('$<EMAIL>').isValid, false);
        expect(emailAuthService.validatePassword(longString).isValid, false);
        expect(emailAuthService.validateFirstName(longString), isNotNull);
        expect(emailAuthService.validateLastName(longString), isNotNull);
      });

      test('should handle special Unicode characters', () {
        // Arabic names should work
        expect(emailAuthService.validateFirstName('محمد'), isNull);
        expect(emailAuthService.validateLastName('العلي'), isNull);
        
        // Emoji and other Unicode should be rejected
        expect(emailAuthService.validateFirstName('Ahmed😀'), isNotNull);
        expect(emailAuthService.validateLastName('Smith🚗'), isNotNull);
      });

      test('should maintain consistent validation behavior', () {
        const email = '<EMAIL>';
        const password = 'MyStr0ng!Pass';
        
        // Multiple calls should return same result
        final result1 = emailAuthService.validateEmail(email);
        final result2 = emailAuthService.validateEmail(email);
        final result3 = emailAuthService.validatePassword(password);
        final result4 = emailAuthService.validatePassword(password);
        
        expect(result1.isValid, result2.isValid);
        expect(result3.isValid, result4.isValid);
        expect(result3.strength, result4.strength);
      });
    });
  });
}
