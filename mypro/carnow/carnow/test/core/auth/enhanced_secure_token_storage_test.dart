/// ============================================================================
/// ENHANCED SECURE TOKEN STORAGE TESTS - Forever Plan Architecture
/// ============================================================================
///
/// اختبارات خدمة التخزين الآمن المحسّنة - بنية الخطة الدائمة
/// Comprehensive unit tests for secure token storage service
///
/// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
/// ✅ Tests all ITokenStorage interface methods
/// ✅ Tests encryption and decryption functionality
/// ✅ Tests token expiry validation and cleanup
/// ✅ Tests storage integrity validation
/// ✅ Tests error handling and edge cases
/// ============================================================================
library;

import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
// import 'package:local_auth/local_auth.dart'; // Commented out for now
import 'package:mocktail/mocktail.dart';

import 'package:carnow/core/auth/enhanced_secure_token_storage.dart';

// =============================================================================
// MOCK CLASSES
// =============================================================================

class MockFlutterSecureStorage extends Mock implements FlutterSecureStorage {}
// class MockLocalAuthentication extends Mock implements LocalAuthentication {} // Commented out for now

// =============================================================================
// TEST SETUP
// =============================================================================

void main() {
  group('EnhancedSecureTokenStorage', () {
    late EnhancedSecureTokenStorage storage;
    late MockFlutterSecureStorage mockSecureStorage;

    setUp(() {
      mockSecureStorage = MockFlutterSecureStorage();
      storage = EnhancedSecureTokenStorage(
        secureStorage: mockSecureStorage,
      );
    });

    tearDown(() {
      reset(mockSecureStorage);
    });

    // =========================================================================
    // Token Storage Operations Tests
    // =========================================================================

    group('Token Storage Operations', () {
      test('should store access token successfully', () async {
        // Arrange
        const token = 'test_access_token';
        final expiryDate = DateTime.now().add(const Duration(hours: 1));
        
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);
        when(() => mockSecureStorage.write(key: any(named: 'key'), value: any(named: 'value')))
            .thenAnswer((_) async {});

        // Act
        await storage.storeToken(token, expiryDate: expiryDate);

        // Assert
        verify(() => mockSecureStorage.write(
          key: 'carnow_access_token_v2',
          value: any(named: 'value'),
        )).called(1);
      });

      test('should store refresh token successfully', () async {
        // Arrange
        const refreshToken = 'test_refresh_token';
        
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);
        when(() => mockSecureStorage.write(key: any(named: 'key'), value: any(named: 'value')))
            .thenAnswer((_) async {});

        // Act
        await storage.storeRefreshToken(refreshToken);

        // Assert
        verify(() => mockSecureStorage.write(
          key: 'carnow_refresh_token_v2',
          value: any(named: 'value'),
        )).called(1);
      });

      test('should store session data successfully', () async {
        // Arrange
        final sessionData = {'userId': '123', 'sessionId': 'abc'};
        
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);
        when(() => mockSecureStorage.write(key: any(named: 'key'), value: any(named: 'value')))
            .thenAnswer((_) async {});

        // Act
        await storage.storeSessionData(sessionData);

        // Assert
        verify(() => mockSecureStorage.write(
          key: 'carnow_session_data_v2',
          value: any(named: 'value'),
        )).called(1);
      });

      test('should store token expiry successfully', () async {
        // Arrange
        final expiryDate = DateTime.now().add(const Duration(hours: 1));
        
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);
        when(() => mockSecureStorage.write(key: any(named: 'key'), value: any(named: 'value')))
            .thenAnswer((_) async {});

        // Act
        await storage.storeTokenExpiry(expiryDate);

        // Assert
        verify(() => mockSecureStorage.write(
          key: 'carnow_token_expiry_v2',
          value: any(named: 'value'),
        )).called(1);
      });
    });

    // =========================================================================
    // Token Retrieval Operations Tests
    // =========================================================================

    group('Token Retrieval Operations', () {
      test('should retrieve access token successfully', () async {
        // Arrange
        const token = 'test_access_token';
        final encodedToken = base64Encode(utf8.encode(token));
        final futureExpiry = DateTime.now().add(const Duration(hours: 1));
        final encodedExpiry = base64Encode(utf8.encode(futureExpiry.toIso8601String()));
        
        when(() => mockSecureStorage.read(key: 'carnow_access_token_v2'))
            .thenAnswer((_) async => encodedToken);
        when(() => mockSecureStorage.read(key: 'carnow_token_expiry_v2'))
            .thenAnswer((_) async => encodedExpiry);
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);
        when(() => mockSecureStorage.write(key: any(named: 'key'), value: any(named: 'value')))
            .thenAnswer((_) async {});

        // Act
        final result = await storage.getToken();

        // Assert
        expect(result, equals(token));
      });

      test('should return null for non-existent token', () async {
        // Arrange
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);

        // Act
        final result = await storage.getToken();

        // Assert
        expect(result, isNull);
      });

      test('should retrieve refresh token successfully', () async {
        // Arrange
        const refreshToken = 'test_refresh_token';
        final encodedRefreshToken = base64Encode(utf8.encode(refreshToken));
        
        when(() => mockSecureStorage.read(key: 'carnow_refresh_token_v2'))
            .thenAnswer((_) async => encodedRefreshToken);
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);
        when(() => mockSecureStorage.write(key: any(named: 'key'), value: any(named: 'value')))
            .thenAnswer((_) async {});

        // Act
        final result = await storage.getRefreshToken();

        // Assert
        expect(result, equals(refreshToken));
      });

      test('should retrieve session data successfully', () async {
        // Arrange
        final sessionData = {'userId': '123', 'sessionId': 'abc'};
        final encodedSessionData = base64Encode(utf8.encode(jsonEncode(sessionData)));
        
        when(() => mockSecureStorage.read(key: 'carnow_session_data_v2'))
            .thenAnswer((_) async => encodedSessionData);
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);
        when(() => mockSecureStorage.write(key: any(named: 'key'), value: any(named: 'value')))
            .thenAnswer((_) async {});

        // Act
        final result = await storage.getSessionData();

        // Assert
        expect(result, equals(sessionData));
      });

      test('should return empty map for non-existent session data', () async {
        // Arrange
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);

        // Act
        final result = await storage.getSessionData();

        // Assert
        expect(result, isEmpty);
      });

      test('should retrieve token expiry successfully', () async {
        // Arrange
        final expiryDate = DateTime.now().add(const Duration(hours: 1));
        final encodedExpiry = base64Encode(utf8.encode(expiryDate.toIso8601String()));
        
        when(() => mockSecureStorage.read(key: 'carnow_token_expiry_v2'))
            .thenAnswer((_) async => encodedExpiry);
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);
        when(() => mockSecureStorage.write(key: any(named: 'key'), value: any(named: 'value')))
            .thenAnswer((_) async {});

        // Act
        final result = await storage.getTokenExpiry();

        // Assert
        expect(result, isNotNull);
        expect(result!.difference(expiryDate).inSeconds, lessThan(1));
      });
    });

    // =========================================================================
    // Token Validation Operations Tests
    // =========================================================================

    group('Token Validation Operations', () {
      test('should return true for valid token', () async {
        // Arrange
        const token = 'test_access_token';
        final encodedToken = base64Encode(utf8.encode(token));
        final futureExpiry = DateTime.now().add(const Duration(hours: 1));
        final encodedExpiry = base64Encode(utf8.encode(futureExpiry.toIso8601String()));
        
        when(() => mockSecureStorage.read(key: 'carnow_access_token_v2'))
            .thenAnswer((_) async => encodedToken);
        when(() => mockSecureStorage.read(key: 'carnow_token_expiry_v2'))
            .thenAnswer((_) async => encodedExpiry);
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);
        when(() => mockSecureStorage.write(key: any(named: 'key'), value: any(named: 'value')))
            .thenAnswer((_) async {});

        // Act
        final result = await storage.hasValidToken();

        // Assert
        expect(result, isTrue);
      });

      test('should return false for expired token', () async {
        // Arrange
        const token = 'test_access_token';
        final encodedToken = base64Encode(utf8.encode(token));
        final pastExpiry = DateTime.now().subtract(const Duration(hours: 1));
        final encodedExpiry = base64Encode(utf8.encode(pastExpiry.toIso8601String()));
        
        when(() => mockSecureStorage.read(key: 'carnow_access_token_v2'))
            .thenAnswer((_) async => encodedToken);
        when(() => mockSecureStorage.read(key: 'carnow_token_expiry_v2'))
            .thenAnswer((_) async => encodedExpiry);
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);
        when(() => mockSecureStorage.write(key: any(named: 'key'), value: any(named: 'value')))
            .thenAnswer((_) async {});
        when(() => mockSecureStorage.delete(key: any(named: 'key')))
            .thenAnswer((_) async {});

        // Act
        final result = await storage.hasValidToken();

        // Assert
        expect(result, isFalse);
      });

      test('should return false for non-existent token', () async {
        // Arrange
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);

        // Act
        final result = await storage.hasValidToken();

        // Assert
        expect(result, isFalse);
      });

      test('should return true for valid refresh token', () async {
        // Arrange
        const refreshToken = 'test_refresh_token';
        final encodedRefreshToken = base64Encode(utf8.encode(refreshToken));
        
        when(() => mockSecureStorage.read(key: 'carnow_refresh_token_v2'))
            .thenAnswer((_) async => encodedRefreshToken);
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);
        when(() => mockSecureStorage.write(key: any(named: 'key'), value: any(named: 'value')))
            .thenAnswer((_) async {});

        // Act
        final result = await storage.hasValidRefreshToken();

        // Assert
        expect(result, isTrue);
      });

      test('should detect expired token correctly', () async {
        // Arrange
        final pastExpiry = DateTime.now().subtract(const Duration(hours: 1));
        final encodedExpiry = base64Encode(utf8.encode(pastExpiry.toIso8601String()));
        
        when(() => mockSecureStorage.read(key: 'carnow_token_expiry_v2'))
            .thenAnswer((_) async => encodedExpiry);
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);
        when(() => mockSecureStorage.write(key: any(named: 'key'), value: any(named: 'value')))
            .thenAnswer((_) async {});

        // Act
        final result = await storage.isTokenExpired();

        // Assert
        expect(result, isTrue);
      });

      test('should calculate time until expiry correctly', () async {
        // Arrange
        final futureExpiry = DateTime.now().add(const Duration(hours: 2));
        final encodedExpiry = base64Encode(utf8.encode(futureExpiry.toIso8601String()));
        
        when(() => mockSecureStorage.read(key: 'carnow_token_expiry_v2'))
            .thenAnswer((_) async => encodedExpiry);
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);
        when(() => mockSecureStorage.write(key: any(named: 'key'), value: any(named: 'value')))
            .thenAnswer((_) async {});

        // Act
        final result = await storage.getTimeUntilExpiry();

        // Assert
        expect(result, isNotNull);
        expect(result!.inHours, equals(1)); // Should be approximately 2 hours
      });
    });

    // =========================================================================
    // Token Management Operations Tests
    // =========================================================================

    group('Token Management Operations', () {
      test('should clear all data successfully', () async {
        // Arrange
        when(() => mockSecureStorage.delete(key: any(named: 'key')))
            .thenAnswer((_) async {});

        // Act
        await storage.clearAllData();

        // Assert
        verify(() => mockSecureStorage.delete(key: 'carnow_access_token_v2')).called(1);
        verify(() => mockSecureStorage.delete(key: 'carnow_refresh_token_v2')).called(1);
        verify(() => mockSecureStorage.delete(key: 'carnow_token_expiry_v2')).called(1);
        verify(() => mockSecureStorage.delete(key: 'carnow_session_data_v2')).called(1);
        verify(() => mockSecureStorage.delete(key: 'carnow_storage_metadata_v2')).called(1);
        verify(() => mockSecureStorage.delete(key: 'carnow_integrity_hash_v2')).called(1);
      });

      test('should clear expired tokens successfully', () async {
        // Arrange
        final pastExpiry = DateTime.now().subtract(const Duration(hours: 1));
        final encodedExpiry = base64Encode(utf8.encode(pastExpiry.toIso8601String()));
        
        when(() => mockSecureStorage.read(key: 'carnow_token_expiry_v2'))
            .thenAnswer((_) async => encodedExpiry);
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);
        when(() => mockSecureStorage.write(key: any(named: 'key'), value: any(named: 'value')))
            .thenAnswer((_) async {});
        when(() => mockSecureStorage.delete(key: any(named: 'key')))
            .thenAnswer((_) async {});

        // Act
        await storage.clearExpiredTokens();

        // Assert
        verify(() => mockSecureStorage.delete(key: 'carnow_access_token_v2')).called(1);
        verify(() => mockSecureStorage.delete(key: 'carnow_token_expiry_v2')).called(1);
      });

      test('should update token expiry successfully', () async {
        // Arrange
        final newExpiry = DateTime.now().add(const Duration(hours: 3));
        
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);
        when(() => mockSecureStorage.write(key: any(named: 'key'), value: any(named: 'value')))
            .thenAnswer((_) async {});

        // Act
        await storage.updateTokenExpiry(newExpiry);

        // Assert
        verify(() => mockSecureStorage.write(
          key: 'carnow_token_expiry_v2',
          value: any(named: 'value'),
        )).called(1);
      });

      test('should rotate tokens successfully', () async {
        // Arrange
        const newToken = 'new_access_token';
        const newRefreshToken = 'new_refresh_token';
        final newExpiry = DateTime.now().add(const Duration(hours: 2));
        
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);
        when(() => mockSecureStorage.write(key: any(named: 'key'), value: any(named: 'value')))
            .thenAnswer((_) async {});

        // Act
        await storage.rotateTokens(
          newToken: newToken,
          newRefreshToken: newRefreshToken,
          newExpiryDate: newExpiry,
        );

        // Assert
        verify(() => mockSecureStorage.write(
          key: 'carnow_access_token_v2',
          value: any(named: 'value'),
        )).called(1);
        verify(() => mockSecureStorage.write(
          key: 'carnow_refresh_token_v2',
          value: any(named: 'value'),
        )).called(1);
      });
    });

    // =========================================================================
    // Security Operations Tests
    // =========================================================================

    group('Security Operations', () {
      test('should validate storage integrity successfully', () async {
        // Arrange
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);

        // Act
        final result = await storage.validateStorageIntegrity();

        // Assert
        expect(result, isTrue); // Should return true when no hash is stored
      });

      test('should return storage metadata', () async {
        // Arrange
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);

        // Act
        final result = await storage.getStorageMetadata();

        // Assert
        expect(result, isA<Map<String, dynamic>>());
      });

      test('should report encryption status correctly', () {
        // Act & Assert
        expect(storage.isEncrypted, isTrue);
      });

      test('should check biometric support', () async {
        // Act
        final result = await storage.supportsBiometrics;

        // Assert
        expect(result, isFalse); // Currently returns false as biometrics not implemented
      });
    });

    // =========================================================================
    // Error Handling Tests
    // =========================================================================

    group('Error Handling', () {
      test('should handle storage write errors gracefully', () async {
        // Arrange
        when(() => mockSecureStorage.write(key: any(named: 'key'), value: any(named: 'value')))
            .thenThrow(Exception('Storage write failed'));
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);

        // Act & Assert
        expect(
          () => storage.storeToken('test_token'),
          throwsA(isA<Exception>()),
        );
      });

      test('should handle storage read errors gracefully', () async {
        // Arrange
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenThrow(Exception('Storage read failed'));

        // Act
        final result = await storage.getToken();

        // Assert
        expect(result, isNull);
      });

      test('should handle decryption errors gracefully', () async {
        // Arrange
        when(() => mockSecureStorage.read(key: 'carnow_access_token_v2'))
            .thenAnswer((_) async => 'invalid_base64');
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);

        // Act
        final result = await storage.getToken();

        // Assert
        expect(result, isNull);
      });
    });

    // =========================================================================
    // Integration Tests
    // =========================================================================

    group('Integration Tests', () {
      test('should perform complete token lifecycle', () async {
        // Arrange
        const token = 'test_access_token';
        const refreshToken = 'test_refresh_token';
        final expiryDate = DateTime.now().add(const Duration(hours: 1));
        final sessionData = {'userId': '123', 'sessionId': 'abc'};
        
        when(() => mockSecureStorage.read(key: any(named: 'key')))
            .thenAnswer((_) async => null);
        when(() => mockSecureStorage.write(key: any(named: 'key'), value: any(named: 'value')))
            .thenAnswer((_) async {});
        when(() => mockSecureStorage.delete(key: any(named: 'key')))
            .thenAnswer((_) async {});

        // Act & Assert - Store tokens
        await storage.storeToken(token, expiryDate: expiryDate);
        await storage.storeRefreshToken(refreshToken);
        await storage.storeSessionData(sessionData);

        // Verify storage calls
        verify(() => mockSecureStorage.write(
          key: 'carnow_access_token_v2',
          value: any(named: 'value'),
        )).called(1);
        verify(() => mockSecureStorage.write(
          key: 'carnow_refresh_token_v2',
          value: any(named: 'value'),
        )).called(1);
        verify(() => mockSecureStorage.write(
          key: 'carnow_session_data_v2',
          value: any(named: 'value'),
        )).called(1);

        // Act & Assert - Clear all data
        await storage.clearAllData();
        
        verify(() => mockSecureStorage.delete(key: 'carnow_access_token_v2')).called(1);
        verify(() => mockSecureStorage.delete(key: 'carnow_refresh_token_v2')).called(1);
        verify(() => mockSecureStorage.delete(key: 'carnow_token_expiry_v2')).called(1);
        verify(() => mockSecureStorage.delete(key: 'carnow_session_data_v2')).called(1);
      });
    });
  });
}
