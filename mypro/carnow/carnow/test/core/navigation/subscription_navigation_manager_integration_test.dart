import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import 'package:carnow/core/models/subscription_response.dart';
import 'package:carnow/features/seller/navigation/subscription_navigation_manager.dart';

void main() {
  group('SubscriptionNavigationManager Integration Tests', () {
    late Widget testWidget;

    setUp(() {
      testWidget = MaterialApp.router(
        routerConfig: GoRouter(
          routes: [
            GoRoute(
              path: '/',
              builder: (context, state) => const Scaffold(body: Text('Home')),
            ),
            GoRoute(
              path: '/seller/subscription/form',
              builder: (context, state) =>
                  const Scaffold(body: Text('Subscription Form')),
            ),
            GoRoute(
              path: '/seller/subscription/status/:id',
              builder: (context, state) => Scaffold(
                body: Text(
                  'Subscription Status: ${state.pathParameters['id']}',
                ),
              ),
            ),
            GoRoute(
              path: '/seller/subscription/success/:id',
              builder: (context, state) => Scaffold(
                body: Text(
                  'Subscription Success: ${state.pathParameters['id']}',
                ),
              ),
            ),
            GoRoute(
              path: '/seller/subscription/plans',
              builder: (context, state) =>
                  const Scaffold(body: Text('Subscription Plans')),
            ),
            GoRoute(
              path: '/seller/subscription/upgrade',
              builder: (context, state) =>
                  const Scaffold(body: Text('Subscription Upgrade')),
            ),
          ],
        ),
      );
    });

    testWidgets('should navigate to subscription status successfully', (
      tester,
    ) async {
      await tester.pumpWidget(testWidget);

      final context = tester.element(find.byType(MaterialApp));
      const subscriptionId = 'test-subscription-123';

      // Navigate to subscription status
      await SubscriptionNavigationManager.navigateToSubscriptionStatus(
        context: context,
        subscriptionId: subscriptionId,
      );

      await tester.pumpAndSettle();

      // Verify navigation occurred
      expect(find.text('Subscription Status: $subscriptionId'), findsOneWidget);
    });

    testWidgets('should navigate to subscription form successfully', (
      tester,
    ) async {
      await tester.pumpWidget(testWidget);

      final context = tester.element(find.byType(MaterialApp));

      // Navigate to subscription form
      await SubscriptionNavigationManager.navigateToSubscriptionForm(
        context: context,
      );

      await tester.pumpAndSettle();

      // Verify navigation occurred
      expect(find.text('Subscription Form'), findsOneWidget);
    });

    testWidgets('should navigate to subscription success successfully', (
      tester,
    ) async {
      await tester.pumpWidget(testWidget);

      final context = tester.element(find.byType(MaterialApp));
      final mockResponse = SubscriptionResponse(
        id: 'test-request-123',
        status: 'success',
        createdAt: DateTime.now(),
        storeName: 'Test Store',
        planType: 'basic',
        price: 200.0,
        userId: 'seller-123',
      );

      // Navigate to subscription success
      await SubscriptionNavigationManager.navigateToSubscriptionSuccess(
        context: context,
        response: mockResponse,
      );

      await tester.pumpAndSettle();

      // Verify navigation occurred
      expect(
        find.text('Subscription Success: ${mockResponse.id}'),
        findsOneWidget,
      );
    });

    testWidgets('should navigate to subscription plans successfully', (
      tester,
    ) async {
      await tester.pumpWidget(testWidget);

      final context = tester.element(find.byType(MaterialApp));

      // Navigate to subscription plans
      await SubscriptionNavigationManager.navigateToSubscriptionPlans(
        context: context,
      );

      await tester.pumpAndSettle();

      // Verify navigation occurred
      expect(find.text('Subscription Plans'), findsOneWidget);
    });

    testWidgets('should navigate to subscription upgrade successfully', (
      tester,
    ) async {
      await tester.pumpWidget(testWidget);

      final context = tester.element(find.byType(MaterialApp));

      // Navigate to subscription upgrade
      await SubscriptionNavigationManager.navigateToSubscriptionUpgrade(
        context: context,
        currentPlanId: 'basic-plan',
      );

      await tester.pumpAndSettle();

      // Verify navigation occurred
      expect(find.text('Subscription Upgrade'), findsOneWidget);
    });

    testWidgets('should handle navigation errors gracefully', (tester) async {
      // Create a widget with broken routing
      final brokenWidget = MaterialApp.router(
        routerConfig: GoRouter(
          routes: [
            GoRoute(
              path: '/',
              builder: (context, state) => const Scaffold(body: Text('Home')),
            ),
            // Missing subscription routes to trigger errors
          ],
        ),
      );

      await tester.pumpWidget(brokenWidget);

      final context = tester.element(find.byType(MaterialApp));

      // Attempt navigation that should fail
      expect(
        () => SubscriptionNavigationManager.navigateToSubscriptionForm(
          context: context,
        ),
        throwsA(isA<SubscriptionNavigationError>()),
      );
    });

    testWidgets('should show error dialog on navigation failure', (
      tester,
    ) async {
      // Create a widget with broken routing
      final brokenWidget = MaterialApp.router(
        routerConfig: GoRouter(
          routes: [
            GoRoute(
              path: '/',
              builder: (context, state) => const Scaffold(body: Text('Home')),
            ),
          ],
        ),
      );

      await tester.pumpWidget(brokenWidget);

      final context = tester.element(find.byType(MaterialApp));

      // Attempt navigation that should fail and show dialog
      try {
        await SubscriptionNavigationManager.navigateToSubscriptionForm(
          context: context,
        );
      } catch (e) {
        // Expected to throw
      }

      await tester.pumpAndSettle();

      // Verify error dialog is shown
      expect(find.text('خطأ في التنقل'), findsOneWidget);
      expect(
        find.text('تعذر الانتقال إلى نموذج الاشتراك. يرجى المحاولة مرة أخرى.'),
        findsOneWidget,
      );
    });

    testWidgets('should handle back navigation safely', (tester) async {
      await tester.pumpWidget(testWidget);

      final context = tester.element(find.byType(MaterialApp));

      // Navigate to a screen first
      await SubscriptionNavigationManager.navigateToSubscriptionForm(
        context: context,
      );
      await tester.pumpAndSettle();

      // Go back
      SubscriptionNavigationManager.goBack(context);
      await tester.pumpAndSettle();

      // Should be back at home
      expect(find.text('Home'), findsOneWidget);
    });

    testWidgets('should navigate to root safely', (tester) async {
      await tester.pumpWidget(testWidget);

      final context = tester.element(find.byType(MaterialApp));

      // Navigate to a screen first
      await SubscriptionNavigationManager.navigateToSubscriptionForm(
        context: context,
      );
      await tester.pumpAndSettle();

      // Navigate to root
      SubscriptionNavigationManager.navigateToRoot(context);
      await tester.pumpAndSettle();

      // Should be at home
      expect(find.text('Home'), findsOneWidget);
    });

    group('Error Handling', () {
      testWidgets('should handle SubscriptionNavigationError correctly', (
        tester,
      ) async {
        await tester.pumpWidget(testWidget);

        final context = tester.element(find.byType(MaterialApp));
        final error = SubscriptionNavigationError(
          message: 'Test navigation error',
          attemptedRoute: '/test/route',
        );

        // Handle the error
        await SubscriptionNavigationManager.handleNavigationError(
          context: context,
          error: error,
        );

        await tester.pumpAndSettle();

        // Verify error dialog is shown
        expect(find.text('خطأ في التنقل'), findsOneWidget);
        expect(find.text('Test navigation error'), findsOneWidget);
      });

      testWidgets('should convert SubscriptionNavigationError to AppError', (
        tester,
      ) async {
        final error = SubscriptionNavigationError(
          message: 'Test navigation error',
          attemptedRoute: '/test/route',
          originalError: Exception('Original error'),
        );

        final appError = error.toAppError();

        expect(appError.message, 'Test navigation error');
        expect(appError.code, 'NAVIGATION_ERROR');
        expect(appError.originalError, isA<Exception>());
      });
    });

    group('Performance Tests', () {
      testWidgets('should handle rapid navigation requests', (tester) async {
        await tester.pumpWidget(testWidget);

        final context = tester.element(find.byType(MaterialApp));
        final stopwatch = Stopwatch()..start();

        // Perform rapid navigation
        for (int i = 0; i < 5; i++) {
          await SubscriptionNavigationManager.navigateToSubscriptionForm(
            context: context,
          );
          await tester.pump();

          SubscriptionNavigationManager.goBack(context);
          await tester.pump();
        }

        stopwatch.stop();

        // Should complete within reasonable time
        expect(stopwatch.elapsedMilliseconds, lessThan(2000));
      });

      testWidgets('should handle concurrent navigation requests', (
        tester,
      ) async {
        await tester.pumpWidget(testWidget);

        final context = tester.element(find.byType(MaterialApp));

        // Start multiple navigation operations
        final futures = <Future<void>>[];

        for (int i = 0; i < 3; i++) {
          futures.add(
            SubscriptionNavigationManager.navigateToSubscriptionForm(
              context: context,
            ).catchError((e) {
              // Ignore errors for concurrent test
              return null;
            }),
          );
        }

        await Future.wait(futures);
        await tester.pumpAndSettle();

        // Should handle concurrent requests without crashing
        expect(find.byType(MaterialApp), findsOneWidget);
      });
    });

    group('Route Parameter Tests', () {
      testWidgets('should pass subscription ID correctly in navigation', (
        tester,
      ) async {
        await tester.pumpWidget(testWidget);

        final context = tester.element(find.byType(MaterialApp));
        const subscriptionId = 'test-subscription-with-special-chars-123_abc';

        await SubscriptionNavigationManager.navigateToSubscriptionStatus(
          context: context,
          subscriptionId: subscriptionId,
        );

        await tester.pumpAndSettle();

        expect(
          find.text('Subscription Status: $subscriptionId'),
          findsOneWidget,
        );
      });

      testWidgets('should handle empty subscription ID gracefully', (
        tester,
      ) async {
        await tester.pumpWidget(testWidget);

        final context = tester.element(find.byType(MaterialApp));

        // This should either handle empty ID or throw appropriate error
        try {
          await SubscriptionNavigationManager.navigateToSubscriptionStatus(
            context: context,
            subscriptionId: '',
          );
          await tester.pumpAndSettle();
        } catch (e) {
          expect(e, isA<SubscriptionNavigationError>());
        }
      });

      testWidgets('should handle special characters in route parameters', (
        tester,
      ) async {
        await tester.pumpWidget(testWidget);

        final context = tester.element(find.byType(MaterialApp));
        const subscriptionId =
            'test-subscription-with-special-chars-!@#\$%^&*()';

        try {
          await SubscriptionNavigationManager.navigateToSubscriptionStatus(
            context: context,
            subscriptionId: subscriptionId,
          );
          await tester.pumpAndSettle();
        } catch (e) {
          // Special characters might cause navigation issues, which is expected
          expect(e, isA<SubscriptionNavigationError>());
        }
      });
    });

    group('Context Safety Tests', () {
      testWidgets('should handle unmounted context safely', (tester) async {
        await tester.pumpWidget(testWidget);

        final context = tester.element(find.byType(MaterialApp));

        // Remove the widget to unmount context
        await tester.pumpWidget(const SizedBox());

        // Navigation should handle unmounted context gracefully
        expect(
          () => SubscriptionNavigationManager.navigateToSubscriptionForm(
            context: context,
          ),
          throwsA(isA<SubscriptionNavigationError>()),
        );
      });

      testWidgets('should validate context before navigation', (tester) async {
        await tester.pumpWidget(testWidget);

        final context = tester.element(find.byType(MaterialApp));

        // Valid context should work
        await SubscriptionNavigationManager.navigateToSubscriptionForm(
          context: context,
        );

        await tester.pumpAndSettle();
        expect(find.text('Subscription Form'), findsOneWidget);
      });
    });
  });
}
