// =============================================================================
// CarNow Test Configuration
// تكوين اختبارات CarNow الشامل
// =============================================================================

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';

/// Test configuration for CarNow application
/// تكوين الاختبارات لتطبيق CarNow
class TestConfig {
  // Test Environment Configuration
  static const String testEnvironment = 'test';
  static const String testDatabaseUrl =
      'postgresql://test:test@localhost:5432/carnow_test';
  static const String testApiBaseUrl = 'http://localhost:8080/api/v1';

  // Test Timeouts
  static const Duration defaultTimeout = Duration(seconds: 30);
  static const Duration longTimeout = Duration(minutes: 2);
  static const Duration shortTimeout = Duration(seconds: 5);

  // Coverage Targets
  static const double unitTestCoverageTarget = 90.0;
  static const double widgetTestCoverageTarget = 85.0;
  static const double integrationTestCoverageTarget = 80.0;
  static const double e2eTestCoverageTarget = 75.0;
  static const double overallCoverageTarget = 85.0;

  // Test Categories
  static const String unitTestCategory = 'unit';
  static const String widgetTestCategory = 'widget';
  static const String integrationTestCategory = 'integration';
  static const String e2eTestCategory = 'e2e';
  static const String securityTestCategory = 'security';
  static const String performanceTestCategory = 'performance';

  // Mock Data Configuration
  static const int mockDataSeed = 12345;
  static const int defaultMockItemCount = 10;
  static const int largeMockItemCount = 100;

  // Test Database Configuration
  static const String testDbName = 'carnow_test_db';
  static const bool useInMemoryDatabase = true;
  static const bool resetDatabaseBetweenTests = true;

  // API Testing Configuration
  static const bool useMockApi = true;
  static const bool enableApiLogging = kDebugMode;
  static const Duration apiTimeout = Duration(seconds: 10);

  // Widget Testing Configuration
  static const bool enableGoldenTests = true;
  static const bool updateGoldensOnFailure = false;
  static const double goldenTestThreshold = 0.01;

  // Performance Testing Configuration
  static const Duration maxRenderTime = Duration(milliseconds: 16); // 60 FPS
  static const int maxMemoryUsageMB = 100;
  static const Duration maxStartupTime = Duration(seconds: 3);

  // Security Testing Configuration
  static const bool enableSecurityTests = true;
  static const bool testInputValidation = true;
  static const bool testAuthenticationSecurity = true;

  // Accessibility Testing Configuration
  static const bool enableAccessibilityTests = true;
  static const double minTouchTargetSize = 44.0; // iOS HIG minimum
  static const double minColorContrastRatio = 4.5; // WCAG AA standard

  /// Initialize test configuration
  static void initialize() {
    // Set up test environment
    if (kDebugMode) {
      print('🧪 Initializing CarNow Test Configuration');
      print('   Environment: $testEnvironment');
      print('   Coverage Target: $overallCoverageTarget%');
      print('   Mock Data: ${useMockApi ? 'Enabled' : 'Disabled'}');
    }

    // Configure patrol test driver only for integration tests
    if (const bool.fromEnvironment('INTEGRATION_TEST', defaultValue: false)) {
      // Patrol doesn't need explicit binding initialization like integration_test
      // It's handled automatically when using @PatrolTest() annotation
    }
  }

  /// Get test configuration for specific test type
  static Map<String, dynamic> getConfigForTestType(String testType) {
    switch (testType) {
      case unitTestCategory:
        return {
          'timeout': shortTimeout,
          'coverage_target': unitTestCoverageTarget,
          'use_mocks': true,
          'parallel_execution': true,
        };

      case widgetTestCategory:
        return {
          'timeout': defaultTimeout,
          'coverage_target': widgetTestCoverageTarget,
          'enable_golden_tests': enableGoldenTests,
          'accessibility_tests': enableAccessibilityTests,
        };

      case integrationTestCategory:
        return {
          'timeout': longTimeout,
          'coverage_target': integrationTestCoverageTarget,
          'use_real_backend': !useMockApi,
          'reset_state': true,
        };

      case e2eTestCategory:
        return {
          'timeout': longTimeout,
          'coverage_target': e2eTestCoverageTarget,
          'use_real_devices': true,
          'performance_monitoring': true,
        };

      case securityTestCategory:
        return {
          'timeout': defaultTimeout,
          'enable_security_tests': enableSecurityTests,
          'test_input_validation': testInputValidation,
          'test_auth_security': testAuthenticationSecurity,
        };

      default:
        return {
          'timeout': defaultTimeout,
          'coverage_target': overallCoverageTarget,
        };
    }
  }

  /// Check if test should run based on environment
  static bool shouldRunTest(String testCategory, {List<String>? tags}) {
    // Skip long-running tests in CI if not explicitly requested
    if (testCategory == e2eTestCategory &&
        const bool.fromEnvironment('CI', defaultValue: false) &&
        !const bool.fromEnvironment('RUN_E2E_TESTS', defaultValue: false)) {
      return false;
    }

    // Skip security tests if not enabled
    if (testCategory == securityTestCategory && !enableSecurityTests) {
      return false;
    }

    // Check for specific tags
    if (tags != null && tags.isNotEmpty) {
      const enabledTags = String.fromEnvironment('TEST_TAGS', defaultValue: '');
      if (enabledTags.isNotEmpty) {
        final enabledTagsList = enabledTags.split(',');
        return tags.any((tag) => enabledTagsList.contains(tag));
      }
    }

    return true;
  }

  /// Get test data directory path
  static String getTestDataPath(String testType) {
    return 'test/data/$testType/';
  }

  /// Get golden files directory path
  static String getGoldenFilesPath() {
    return 'test/goldens/';
  }

  /// Get coverage output directory
  static String getCoverageOutputPath() {
    return 'coverage/';
  }
}

/// Test environment helper
class TestEnvironment {
  static bool get isCI => const bool.fromEnvironment('CI', defaultValue: false);
  static bool get isDebug => kDebugMode;
  static bool get isProfile => kProfileMode;
  static bool get isRelease => kReleaseMode;

  static String get platform => defaultTargetPlatform.name;
  static bool get isAndroid => defaultTargetPlatform == TargetPlatform.android;
  static bool get isIOS => defaultTargetPlatform == TargetPlatform.iOS;
  static bool get isWeb => kIsWeb;

  /// Check if running on real device
  static bool get isRealDevice => !isCI && (isAndroid || isIOS);

  /// Get appropriate test configuration based on environment
  static Map<String, dynamic> getEnvironmentConfig() {
    return {
      'is_ci': isCI,
      'is_debug': isDebug,
      'platform': platform,
      'is_real_device': isRealDevice,
      'parallel_tests': isCI ? 4 : 2,
      'timeout_multiplier': isCI ? 2.0 : 1.0,
    };
  }
}

/// Test performance monitoring
class TestPerformanceMonitor {
  static final Stopwatch _stopwatch = Stopwatch();
  static final Map<String, Duration> _testDurations = {};

  static void startTest(String testName) {
    _stopwatch.reset();
    _stopwatch.start();
  }

  static void endTest(String testName) {
    _stopwatch.stop();
    _testDurations[testName] = _stopwatch.elapsed;

    if (kDebugMode) {
      print(
        '⏱️ Test "$testName" completed in ${_stopwatch.elapsed.inMilliseconds}ms',
      );
    }
  }

  static Duration? getTestDuration(String testName) {
    return _testDurations[testName];
  }

  static Map<String, Duration> getAllTestDurations() {
    return Map.from(_testDurations);
  }

  static void printPerformanceReport() {
    if (_testDurations.isEmpty) return;

    print('\n📊 Test Performance Report:');
    print('=' * 50);

    final sortedTests = _testDurations.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    for (final entry in sortedTests.take(10)) {
      print('${entry.key}: ${entry.value.inMilliseconds}ms');
    }

    final totalDuration = _testDurations.values.fold(
      Duration.zero,
      (sum, duration) => sum + duration,
    );
    print('Total test time: ${totalDuration.inSeconds}s');
    print('=' * 50);
  }
}

/// Test utilities and helpers
class TestUtils {
  /// Wait for a condition to be true with timeout
  static Future<void> waitForCondition(
    bool Function() condition, {
    Duration timeout = const Duration(seconds: 5),
    Duration interval = const Duration(milliseconds: 100),
  }) async {
    final stopwatch = Stopwatch()..start();

    while (!condition() && stopwatch.elapsed < timeout) {
      await Future.delayed(interval);
    }

    if (!condition()) {
      throw TimeoutException('Condition not met within timeout', timeout);
    }
  }

  /// Pump and settle with custom duration
  static Future<void> pumpAndSettleWithTimeout(
    WidgetTester tester, {
    Duration timeout = const Duration(seconds: 10),
  }) async {
    await tester.pumpAndSettle(timeout);
  }

  /// Generate unique test ID
  static String generateTestId() {
    return 'test_${DateTime.now().millisecondsSinceEpoch}';
  }
}

/// Custom test annotations
class TestAnnotations {
  /// Mark test as slow (will be skipped in quick test runs)
  static const String slow = 'slow';

  /// Mark test as flaky (needs investigation)
  static const String flaky = 'flaky';

  /// Mark test as critical (must always pass)
  static const String critical = 'critical';

  /// Mark test as integration test
  static const String integration = 'integration';

  /// Mark test as e2e test
  static const String e2e = 'e2e';

  /// Mark test as security test
  static const String security = 'security';

  /// Mark test as performance test
  static const String performance = 'performance';
}
