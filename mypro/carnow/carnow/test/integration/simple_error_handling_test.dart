import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/core/errors/unified_error_handler.dart';
import 'package:carnow/core/errors/app_error.dart';
import 'package:carnow/core/errors/result.dart';

void main() {
  // Patrol doesn't need explicit binding initialization
  // It's handled automatically when using @PatrolTest() annotation

  group('Simple Error Handling Tests', () {
    late UnifiedErrorHandler errorHandler;

    setUp(() {
      errorHandler = UnifiedErrorHandler();
    });

    testWidgets('should handle basic errors correctly', (tester) async {
      // Test network error
      final networkError = Exception('Network connection failed');
      final result = errorHandler.handleError(
        networkError,
        context: 'network_test',
      );

      expect(result, isA<AppError>());
      expect(result.type, equals(AppErrorType.network));
      expect(result.message, contains('Network'));
    });

    testWidgets('should handle authentication errors', (tester) async {
      // Test auth error
      final authError = Exception('Authentication failed');
      final result = errorHandler.handleError(
        authError,
        context: 'auth_test',
      );

      expect(result, isA<AppError>());
      expect(result.message, contains('Authentication'));
    });

    testWidgets('should execute operations with error handling', (tester) async {
      // Test successful operation
      final successResult = await errorHandler.executeWithErrorHandling<String>(
        () async => 'success',
        context: 'test_operation',
      );

      expect(successResult.isSuccess, isTrue);
      expect(successResult.dataOrNull, equals('success'));

      // Test failed operation
      final failResult = await errorHandler.executeWithErrorHandling<String>(
        () async => throw Exception('Operation failed'),
        context: 'test_operation',
      );

      expect(failResult.isFailure, isTrue);
      expect(failResult.errorOrNull, isA<AppError>());
    });

    testWidgets('should handle retry logic for operations', (tester) async {
      int attempts = 0;
      
      final result = await errorHandler.executeWithErrorHandling<String>(
        () async {
          attempts++;
          if (attempts < 3) {
            throw Exception('Temporary failure');
          }
          return 'success after retry';
        },
        context: 'retry_test',
        shouldRetry: true,
        maxRetries: 3,
      );

      expect(result.isSuccess, isTrue);
      expect(result.dataOrNull, equals('success after retry'));
      expect(attempts, equals(3));
    });

    testWidgets('should display errors to user correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    final error = AppError.network(
                      message: 'Test network error',
                    );
                    errorHandler.showErrorToUser(context, error);
                  },
                  child: const Text('Trigger Error'),
                );
              },
            ),
          ),
        ),
      );

      // Tap button to trigger error
      await tester.tap(find.text('Trigger Error'));
      await tester.pump();

      // Verify error is displayed (basic check)
      expect(find.byType(SnackBar), findsOneWidget);
    });
  });
} 