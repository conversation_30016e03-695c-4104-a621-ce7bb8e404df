// Comprehensive tests for CarnowWallet model
// اختبارات شاملة لنموذج CarnowWallet

import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/core/models/carnow_wallet.dart';

void main() {
  group('CarnowWallet Model Tests', () {
    late Map<String, dynamic> validWalletJson;
    late CarnowWallet validWallet;
    
    setUp(() {
      validWalletJson = {
        'id': 'wallet_123',
        'user_id': 'user_456',
        'balance': 1500.50,
        'available_balance': 1200.50,
        'frozen_balance': 300.00,
        'currency': 'LYD',
        'is_active': true,
        'created_at': '2024-01-01T00:00:00.000Z',
        'updated_at': '2024-01-02T00:00:00.000Z',
        'last_transaction_id': 'txn_789',
        'last_transaction_date': '2024-01-02T12:00:00.000Z',
      };
      
      validWallet = CarnowWallet(
        id: 'wallet_123',
        userId: 'user_456',
        balance: 1500.50,
        availableBalance: 1200.50,
        frozenBalance: 300.00,
        currency: 'LYD',
        isActive: true,
        createdAt: DateTime.parse('2024-01-01T00:00:00.000Z'),
        updatedAt: DateTime.parse('2024-01-02T00:00:00.000Z'),
        lastTransactionId: 'txn_789',
        lastTransactionDate: DateTime.parse('2024-01-02T12:00:00.000Z'),
      );
    });
    
    group('CarnowWallet Creation', () {
      test('should create CarnowWallet with required fields only', () {
        final wallet = CarnowWallet(
          id: 'wallet_123',
          userId: 'user_456',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        expect(wallet.id, equals('wallet_123'));
        expect(wallet.userId, equals('user_456'));
        expect(wallet.balance, equals(0.0));
        expect(wallet.availableBalance, equals(0.0));
        expect(wallet.frozenBalance, equals(0.0));
        expect(wallet.currency, equals('LYD'));
        expect(wallet.isActive, isTrue);
        expect(wallet.lastTransactionId, isNull);
        expect(wallet.lastTransactionDate, isNull);
      });
      
      test('should create CarnowWallet with all fields', () {
        expect(validWallet.id, equals('wallet_123'));
        expect(validWallet.userId, equals('user_456'));
        expect(validWallet.balance, equals(1500.50));
        expect(validWallet.availableBalance, equals(1200.50));
        expect(validWallet.frozenBalance, equals(300.00));
        expect(validWallet.currency, equals('LYD'));
        expect(validWallet.isActive, isTrue);
        expect(validWallet.lastTransactionId, equals('txn_789'));
        expect(validWallet.lastTransactionDate, isNotNull);
      });
    });
    
    group('JSON Serialization', () {
      test('should parse CarnowWallet from valid JSON', () {
        final wallet = CarnowWallet.fromJson(validWalletJson);
        
        expect(wallet.id, equals('wallet_123'));
        expect(wallet.userId, equals('user_456'));
        expect(wallet.balance, equals(1500.50));
        expect(wallet.availableBalance, equals(1200.50));
        expect(wallet.frozenBalance, equals(300.00));
        expect(wallet.currency, equals('LYD'));
        expect(wallet.isActive, isTrue);
        expect(wallet.lastTransactionId, equals('txn_789'));
      });
      
      test('should convert CarnowWallet to JSON correctly', () {
        final json = validWallet.toJson();
        
        expect(json['id'], equals('wallet_123'));
        expect(json['user_id'], equals('user_456'));
        expect(json['balance'], equals(1500.50));
        expect(json['available_balance'], equals(1200.50));
        expect(json['frozen_balance'], equals(300.00));
        expect(json['currency'], equals('LYD'));
        expect(json['is_active'], isTrue);
        expect(json['last_transaction_id'], equals('txn_789'));
      });
      
      test('should handle minimal JSON with defaults', () {
        final minimalJson = {
          'id': 'wallet_123',
          'user_id': 'user_456',
          'created_at': '2024-01-01T00:00:00.000Z',
          'updated_at': '2024-01-02T00:00:00.000Z',
        };
        
        final wallet = CarnowWallet.fromJson(minimalJson);
        
        expect(wallet.balance, equals(0.0));
        expect(wallet.availableBalance, equals(0.0));
        expect(wallet.frozenBalance, equals(0.0));
        expect(wallet.currency, equals('LYD'));
        expect(wallet.isActive, isTrue);
        expect(wallet.lastTransactionId, isNull);
        expect(wallet.lastTransactionDate, isNull);
      });
    });
    
    group('Balance Calculations', () {
      test('should handle zero balances', () {
        final wallet = CarnowWallet(
          id: 'wallet_123',
          userId: 'user_456',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        expect(wallet.balance, equals(0.0));
        expect(wallet.availableBalance, equals(0.0));
        expect(wallet.frozenBalance, equals(0.0));
      });
      
      test('should handle negative balances', () {
        final wallet = CarnowWallet(
          id: 'wallet_123',
          userId: 'user_456',
          balance: -100.0,
          availableBalance: -50.0,
          frozenBalance: 0.0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        expect(wallet.balance, equals(-100.0));
        expect(wallet.availableBalance, equals(-50.0));
        expect(wallet.frozenBalance, equals(0.0));
      });
      
      test('should handle large balances', () {
        final wallet = CarnowWallet(
          id: 'wallet_123',
          userId: 'user_456',
          balance: 999999.99,
          availableBalance: 888888.88,
          frozenBalance: 111111.11,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        expect(wallet.balance, equals(999999.99));
        expect(wallet.availableBalance, equals(888888.88));
        expect(wallet.frozenBalance, equals(111111.11));
      });
    });
    
    group('Currency Handling', () {
      test('should default to LYD currency', () {
        final wallet = CarnowWallet(
          id: 'wallet_123',
          userId: 'user_456',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        expect(wallet.currency, equals('LYD'));
      });
      
      test('should accept different currencies', () {
        final wallet = CarnowWallet(
          id: 'wallet_123',
          userId: 'user_456',
          currency: 'USD',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        expect(wallet.currency, equals('USD'));
      });
    });
    
    group('DateTime Handling', () {
      test('should parse DateTime fields correctly', () {
        final wallet = CarnowWallet.fromJson(validWalletJson);
        
        expect(wallet.createdAt, equals(DateTime.parse('2024-01-01T00:00:00.000Z')));
        expect(wallet.updatedAt, equals(DateTime.parse('2024-01-02T00:00:00.000Z')));
        expect(wallet.lastTransactionDate, equals(DateTime.parse('2024-01-02T12:00:00.000Z')));
      });
      
      test('should serialize DateTime fields correctly', () {
        final json = validWallet.toJson();
        
        expect(json['created_at'], equals('2024-01-01T00:00:00.000Z'));
        expect(json['updated_at'], equals('2024-01-02T00:00:00.000Z'));
        expect(json['last_transaction_date'], equals('2024-01-02T12:00:00.000Z'));
      });
    });
  });
  
  group('CreateWalletRequest Tests', () {
    test('should create request with required fields', () {
      final request = CreateWalletRequest(
        userId: 'user_123',
      );
      
      expect(request.userId, equals('user_123'));
      expect(request.currency, equals('LYD'));
    });
    
    test('should create request with custom currency', () {
      final request = CreateWalletRequest(
        userId: 'user_123',
        currency: 'USD',
      );
      
      expect(request.userId, equals('user_123'));
      expect(request.currency, equals('USD'));
    });
    
    test('should parse from JSON correctly', () {
      final json = {
        'userId': 'user_123',
        'currency': 'EUR',
      };
      
      final request = CreateWalletRequest.fromJson(json);
      
      expect(request.userId, equals('user_123'));
      expect(request.currency, equals('EUR'));
    });
  });
  
  group('DepositRequest Tests', () {
    test('should create deposit request with required fields', () {
      final request = DepositRequest(
        amount: 500.0,
        description: 'إيداع نقدي',
      );
      
      expect(request.amount, equals(500.0));
      expect(request.description, equals('إيداع نقدي'));
      expect(request.reference, isNull);
      expect(request.metadata, isNull);
    });
    
    test('should create deposit request with all fields', () {
      final request = DepositRequest(
        amount: 1000.0,
        description: 'إيداع بنكي',
        reference: 'REF123',
        metadata: {'source': 'bank_transfer'},
      );
      
      expect(request.amount, equals(1000.0));
      expect(request.description, equals('إيداع بنكي'));
      expect(request.reference, equals('REF123'));
      expect(request.metadata, equals({'source': 'bank_transfer'}));
    });
  });
  
  group('WithdrawRequest Tests', () {
    test('should create withdraw request with required fields', () {
      final request = WithdrawRequest(
        amount: 200.0,
        description: 'سحب نقدي',
      );
      
      expect(request.amount, equals(200.0));
      expect(request.description, equals('سحب نقدي'));
      expect(request.reference, isNull);
      expect(request.metadata, isNull);
    });
    
    test('should create withdraw request with all fields', () {
      final request = WithdrawRequest(
        amount: 750.0,
        description: 'سحب للبنك',
        reference: 'WD456',
        metadata: {'destination': 'bank_account'},
      );
      
      expect(request.amount, equals(750.0));
      expect(request.description, equals('سحب للبنك'));
      expect(request.reference, equals('WD456'));
      expect(request.metadata, equals({'destination': 'bank_account'}));
    });
  });
}
