// Comprehensive tests for ProductModel utilities
// اختبارات شاملة لأدوات ProductModel

import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/core/models/product_model.dart';
import 'package:carnow/features/products/models/product_model.dart' as product_models;

void main() {
  group('ProductModel Export Tests', () {
    test('should export ProductModel correctly', () {
      // Test that the typedef works correctly
      expect(ProductModel, equals(product_models.ProductModel));
    });
    
    test('should create ProductModel instance', () {
      final product = ProductModel(
        id: '1',
        name: 'قطعة غيار محرك',
        price: 150.0,
        categoryId: '1',
        sellerId: 'seller_123',
      );
      
      expect(product, isA<ProductModel>());
      expect(product.id, equals(1));
      expect(product.name, equals('قطعة غيار محرك'));
      expect(product.price, equals(150.0));
    });
  });
  
  group('ProductModelUtils Tests', () {
    late Map<String, dynamic> camelCaseJson;
    late Map<String, dynamic> snakeCaseJson;
    late ProductModel testProduct;
    
    setUp(() {
      camelCaseJson = {
        'id': '1',
        'name': 'قطعة غيار محرك',
        'description': 'قطعة غيار عالية الجودة للمحرك',
        'price': 150.0,
        'categoryId': '5',
        'sellerId': 'seller_123',
        'brand': 'Toyota',
        'model': 'Camry',
        'yearFrom': 2015,
        'yearTo': 2023,
        'isActive': true,
        'stockQuantity': 25,
        'images': ['https://example.com/image.jpg'],
        'createdAt': '2024-01-01T10:00:00.000Z',
        'updatedAt': '2024-01-01T10:30:00.000Z',
      };

      snakeCaseJson = {
        'id': '1',
        'name': 'قطعة غيار محرك',
        'description': 'قطعة غيار عالية الجودة للمحرك',
        'price': 150.0,
        'category_id': '5',
        'seller_id': 'seller_123',
        'brand': 'Toyota',
        'model': 'Camry',
        'year_from': 2015,
        'year_to': 2023,
        'is_active': true,
        'stock_quantity': 25,
        'images': ['https://example.com/image.jpg'],
        'created_at': '2024-01-01T10:00:00.000Z',
        'updated_at': '2024-01-01T10:30:00.000Z',
      };
      
      testProduct = ProductModel(
        id: '1',
        name: 'قطعة غيار محرك',
        description: 'قطعة غيار عالية الجودة للمحرك',
        price: 150.0,
        categoryId: '5',
        sellerId: 'seller_123',
        brand: 'Toyota',
        model: 'Camry',
        yearFrom: 2015,
        yearTo: 2023,
        isActive: true,
        stockQuantity: 25,
        images: ['https://example.com/image.jpg'],
        createdAt: DateTime.parse('2024-01-01T10:00:00.000Z'),
        updatedAt: DateTime.parse('2024-01-01T10:30:00.000Z'),
      );
    });
    
    group('fromMap Method', () {
      test('should convert camelCase JSON to ProductModel', () {
        final product = ProductModelUtils.fromMap(camelCaseJson);
        
        expect(product.id, equals('1'));
        expect(product.name, equals('قطعة غيار محرك'));
        expect(product.description, equals('قطعة غيار عالية الجودة للمحرك'));
        expect(product.price, equals(150.0));
        expect(product.categoryId, equals('5'));
        expect(product.sellerId, equals('seller_123'));
        expect(product.brand, equals('Toyota'));
        expect(product.model, equals('Camry'));
        expect(product.yearFrom, equals(2015));
        expect(product.yearTo, equals(2023));
        expect(product.isActive, isTrue);
        expect(product.stockQuantity, equals(25));
        expect(product.images, contains('https://example.com/image.jpg'));
      });
      
      test('should handle snake_case JSON correctly', () {
        final product = ProductModelUtils.fromMap(snakeCaseJson);
        
        expect(product.id, equals('1'));
        expect(product.name, equals('قطعة غيار محرك'));
        expect(product.categoryId, equals('5'));
        expect(product.brand, equals('Toyota'));
        expect(product.model, equals('Camry'));
      });
      
      test('should handle mixed case JSON', () {
        final mixedJson = {
          'id': '1',
          'name': 'قطعة غيار',
          'categoryId': '5',  // camelCase
          'sellerId': 'seller_123',
          'price': 100.0,
        };

        final product = ProductModelUtils.fromMap(mixedJson);

        expect(product.id, equals('1'));
        expect(product.name, equals('قطعة غيار'));
        expect(product.price, equals(100.0));
      });
      
      test('should handle empty JSON', () {
        final emptyJson = <String, dynamic>{};
        
        final product = ProductModelUtils.fromMap(emptyJson);
        
        expect(product, isA<ProductModel>());
        expect(product.id, isNull);
        expect(product.name, isNull);
        expect(product.price, isNull);
      });
      
      test('should handle null values in JSON', () {
        final jsonWithNulls = {
          'id': 1,
          'name': 'قطعة غيار',
          'description': null,
          'price': 100.0,
          'categoryId': null,
          'isActive': null,
        };
        
        final product = ProductModelUtils.fromMap(jsonWithNulls);
        
        expect(product.id, equals('1'));
        expect(product.name, equals('قطعة غيار'));
        expect(product.description, isNull);
        expect(product.price, equals(100.0));
        expect(product.categoryId, isNull);
        expect(product.isActive, isNull);
      });
    });
    
    group('camelToSnake Conversion', () {
      test('should convert simple camelCase to snake_case', () {
        // Using reflection to test private method through fromMap
        final testCases = {
          'categoryId': 'category_id',
          'brandId': 'brand_id',
          'modelId': 'model_id',
          'yearFrom': 'year_from',
          'yearTo': 'year_to',
          'isActive': 'is_active',
          'stockQuantity': 'stock_quantity',
          'imageUrl': 'image_url',
          'createdAt': 'created_at',
          'updatedAt': 'updated_at',
        };
        
        testCases.forEach((camelCase, expectedSnakeCase) {
          final testJson = {camelCase: 'test_value'};
          // The conversion happens internally in fromMap
          expect(() => ProductModelUtils.fromMap(testJson), returnsNormally);
        });
      });
      
      test('should handle single word correctly', () {
        final testJson = {'name': 'test'};
        final product = ProductModelUtils.fromMap(testJson);
        expect(product.name, equals('test'));
      });
      
      test('should handle multiple capital letters', () {
        final testJson = {'XMLHttpRequest': 'test'};
        // Should not throw error even with unusual field names
        expect(() => ProductModelUtils.fromMap(testJson), returnsNormally);
      });
      
      test('should handle empty string', () {
        final testJson = {'': 'test'};
        expect(() => ProductModelUtils.fromMap(testJson), returnsNormally);
      });
    });
    
    group('toLegacyJson Method', () {
      test('should convert ProductModel to legacy JSON format', () {
        final legacyJson = ProductModelUtils.toLegacyJson(testProduct);
        
        expect(legacyJson, isA<Map<String, dynamic>>());
        expect(legacyJson['id'], equals(1));
        expect(legacyJson['name'], equals('قطعة غيار محرك'));
        expect(legacyJson['description'], equals('قطعة غيار عالية الجودة للمحرك'));
        expect(legacyJson['price'], equals(150.0));
        expect(legacyJson['category_id'], equals(5));
        expect(legacyJson['brand_id'], equals(10));
        expect(legacyJson['model_id'], equals(15));
        expect(legacyJson['year_from'], equals(2015));
        expect(legacyJson['year_to'], equals(2023));
        expect(legacyJson['is_active'], isTrue);
        expect(legacyJson['stock_quantity'], equals(25));
        expect(legacyJson['image_url'], equals('https://example.com/image.jpg'));
      });
      
      test('should handle ProductModel with null fields', () {
        final productWithNulls = ProductModel(
          id: '1',
          name: 'قطعة غيار',
          price: 100.0,
          categoryId: '1',
          sellerId: 'seller_123',
        );
        
        final legacyJson = ProductModelUtils.toLegacyJson(productWithNulls);
        
        expect(legacyJson['id'], equals(1));
        expect(legacyJson['name'], equals('قطعة غيار'));
        expect(legacyJson['price'], equals(100.0));
        expect(legacyJson['description'], isNull);
        expect(legacyJson['category_id'], isNull);
      });
      
      test('should preserve all data types correctly', () {
        final legacyJson = ProductModelUtils.toLegacyJson(testProduct);
        
        expect(legacyJson['id'], isA<int>());
        expect(legacyJson['name'], isA<String>());
        expect(legacyJson['price'], isA<double>());
        expect(legacyJson['is_active'], isA<bool>());
        expect(legacyJson['created_at'], isA<String>());
      });
    });
    
    group('Integration Tests', () {
      test('should handle round-trip conversion correctly', () {
        // Convert to legacy JSON and back
        final legacyJson = ProductModelUtils.toLegacyJson(testProduct);
        final convertedProduct = ProductModelUtils.fromMap(legacyJson);
        
        expect(convertedProduct.id, equals(testProduct.id));
        expect(convertedProduct.name, equals(testProduct.name));
        expect(convertedProduct.description, equals(testProduct.description));
        expect(convertedProduct.price, equals(testProduct.price));
        expect(convertedProduct.categoryId, equals(testProduct.categoryId));
        expect(convertedProduct.brand, equals(testProduct.brand));
        expect(convertedProduct.model, equals(testProduct.model));
        expect(convertedProduct.isActive, equals(testProduct.isActive));
      });
      
      test('should handle Arabic text correctly', () {
        final arabicProduct = ProductModel(
          id: '1',
          name: 'قطعة غيار للمحرك الأمامي',
          description: 'قطعة غيار أصلية عالية الجودة مناسبة للسيارات الحديثة',
          price: 250.0,
          categoryId: '1',
          sellerId: 'seller_123',
        );
        
        final legacyJson = ProductModelUtils.toLegacyJson(arabicProduct);
        final convertedProduct = ProductModelUtils.fromMap(legacyJson);
        
        expect(convertedProduct.name, equals('قطعة غيار للمحرك الأمامي'));
        expect(convertedProduct.description, equals('قطعة غيار أصلية عالية الجودة مناسبة للسيارات الحديثة'));
      });
      
      test('should handle special characters and numbers', () {
        final specialProduct = ProductModel(
          id: '123',
          name: 'Part #ABC-123 (2024)',
          description: 'Special part with symbols: @#\$%^&*()',
          price: 99.99,
          categoryId: '1',
          sellerId: 'seller_123',
        );
        
        final legacyJson = ProductModelUtils.toLegacyJson(specialProduct);
        final convertedProduct = ProductModelUtils.fromMap(legacyJson);
        
        expect(convertedProduct.name, equals('Part #ABC-123 (2024)'));
        expect(convertedProduct.description, equals('Special part with symbols: @#\$%^&*()'));
        expect(convertedProduct.price, equals(99.99));
      });
    });
    
    group('Error Handling', () {
      test('should handle malformed JSON gracefully', () {
        final malformedJson = {
          'id': 'not_a_number',
          'price': 'not_a_double',
          'isActive': 'not_a_boolean',
        };
        
        // Should not throw, but may have unexpected values
        expect(() => ProductModelUtils.fromMap(malformedJson), returnsNormally);
      });
      
      test('should handle very large JSON objects', () {
        final largeJson = <String, dynamic>{};
        for (int i = 0; i < 1000; i++) {
          largeJson['field$i'] = 'value$i';
        }
        largeJson['id'] = 1;
        largeJson['name'] = 'Test Product';
        
        expect(() => ProductModelUtils.fromMap(largeJson), returnsNormally);
      });
    });
  });
}
