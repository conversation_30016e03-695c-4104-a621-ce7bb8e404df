// Comprehensive tests for CarnowUser model
// اختبارات شاملة لنموذج CarnowUser

import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/core/models/carnow_user.dart';

void main() {
  group('CarnowUser Model Tests', () {
    late Map<String, dynamic> validUserJson;
    late CarnowUser validUser;
    
    setUp(() {
      validUserJson = {
        'id': 'user_123',
        'email': '<EMAIL>',
        'first_name': 'أحمد',
        'last_name': 'محمد',
        'phone_number': '+966501234567',
        'avatar_url': 'https://example.com/avatar.jpg',
        'is_active': true,
        'created_at': '2024-01-01T00:00:00.000Z',
        'updated_at': '2024-01-02T00:00:00.000Z',
        'carnow_user_id': 'carnow_456',
      };
      
      validUser = CarnowUser(
        id: 'user_123',
        email: '<EMAIL>',
        firstName: 'أحمد',
        lastName: 'محمد',
        phoneNumber: '+966501234567',
        avatarUrl: 'https://example.com/avatar.jpg',
        isActive: true,
        createdAt: DateTime.parse('2024-01-01T00:00:00.000Z'),
        updatedAt: DateTime.parse('2024-01-02T00:00:00.000Z'),
        carnowUserId: 'carnow_456',
      );
    });
    
    group('CarnowUser Creation', () {
      test('should create CarnowUser with all required fields', () {
        final user = CarnowUser(
          id: 'user_123',
          email: '<EMAIL>',
          firstName: 'أحمد',
          lastName: 'محمد',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        expect(user.id, equals('user_123'));
        expect(user.email, equals('<EMAIL>'));
        expect(user.firstName, equals('أحمد'));
        expect(user.lastName, equals('محمد'));
        expect(user.isActive, isTrue);
        expect(user.phoneNumber, isNull);
        expect(user.avatarUrl, isNull);
        expect(user.carnowUserId, isNull);
      });
      
      test('should create CarnowUser with optional fields', () {
        expect(validUser.phoneNumber, equals('+966501234567'));
        expect(validUser.avatarUrl, equals('https://example.com/avatar.jpg'));
        expect(validUser.carnowUserId, equals('carnow_456'));
      });
    });
    
    group('JSON Serialization', () {
      test('should parse CarnowUser from valid JSON', () {
        final user = CarnowUser.fromJson(validUserJson);
        
        expect(user.id, equals('user_123'));
        expect(user.email, equals('<EMAIL>'));
        expect(user.firstName, equals('أحمد'));
        expect(user.lastName, equals('محمد'));
        expect(user.phoneNumber, equals('+966501234567'));
        expect(user.avatarUrl, equals('https://example.com/avatar.jpg'));
        expect(user.isActive, isTrue);
        expect(user.carnowUserId, equals('carnow_456'));
      });
      
      test('should convert CarnowUser to JSON correctly', () {
        final json = validUser.toJson();
        
        expect(json['id'], equals('user_123'));
        expect(json['email'], equals('<EMAIL>'));
        expect(json['first_name'], equals('أحمد'));
        expect(json['last_name'], equals('محمد'));
        expect(json['phone_number'], equals('+966501234567'));
        expect(json['avatar_url'], equals('https://example.com/avatar.jpg'));
        expect(json['is_active'], isTrue);
        expect(json['carnow_user_id'], equals('carnow_456'));
      });
      
      test('should handle null optional fields in JSON', () {
        final jsonWithNulls = {
          'id': 'user_123',
          'email': '<EMAIL>',
          'first_name': 'أحمد',
          'last_name': 'محمد',
          'phone_number': null,
          'avatar_url': null,
          'is_active': true,
          'created_at': '2024-01-01T00:00:00.000Z',
          'updated_at': '2024-01-02T00:00:00.000Z',
          'carnow_user_id': null,
        };
        
        final user = CarnowUser.fromJson(jsonWithNulls);
        
        expect(user.phoneNumber, isNull);
        expect(user.avatarUrl, isNull);
        expect(user.carnowUserId, isNull);
      });
      
      test('should handle missing optional fields in JSON', () {
        final minimalJson = {
          'id': 'user_123',
          'email': '<EMAIL>',
          'first_name': 'أحمد',
          'last_name': 'محمد',
          'is_active': true,
          'created_at': '2024-01-01T00:00:00.000Z',
          'updated_at': '2024-01-02T00:00:00.000Z',
        };
        
        final user = CarnowUser.fromJson(minimalJson);
        
        expect(user.id, equals('user_123'));
        expect(user.email, equals('<EMAIL>'));
        expect(user.firstName, equals('أحمد'));
        expect(user.lastName, equals('محمد'));
        expect(user.isActive, isTrue);
        expect(user.phoneNumber, isNull);
        expect(user.avatarUrl, isNull);
        expect(user.carnowUserId, isNull);
      });
    });
    
    group('DateTime Handling', () {
      test('should parse DateTime fields correctly', () {
        final user = CarnowUser.fromJson(validUserJson);
        
        expect(user.createdAt, equals(DateTime.parse('2024-01-01T00:00:00.000Z')));
        expect(user.updatedAt, equals(DateTime.parse('2024-01-02T00:00:00.000Z')));
      });
      
      test('should serialize DateTime fields correctly', () {
        final json = validUser.toJson();
        
        expect(json['created_at'], equals('2024-01-01T00:00:00.000Z'));
        expect(json['updated_at'], equals('2024-01-02T00:00:00.000Z'));
      });
    });
    
    group('Equality and Comparison', () {
      test('should be equal when all fields match', () {
        final user1 = CarnowUser.fromJson(validUserJson);
        final user2 = CarnowUser.fromJson(validUserJson);
        
        expect(user1, equals(user2));
        expect(user1.hashCode, equals(user2.hashCode));
      });
      
      test('should not be equal when fields differ', () {
        final user1 = CarnowUser.fromJson(validUserJson);
        final differentJson = Map<String, dynamic>.from(validUserJson);
        differentJson['email'] = '<EMAIL>';
        final user2 = CarnowUser.fromJson(differentJson);
        
        expect(user1, isNot(equals(user2)));
      });
    });
  });
  
  group('CreateCarnowUserRequest Tests', () {
    late Map<String, dynamic> validRequestJson;
    
    setUp(() {
      validRequestJson = {
        'email': '<EMAIL>',
        'first_name': 'سارة',
        'last_name': 'أحمد',
        'phone_number': '+966501234567',
        'avatar_url': 'https://example.com/avatar.jpg',
      };
    });
    
    test('should create request with all fields', () {
      final request = CreateCarnowUserRequest(
        email: '<EMAIL>',
        firstName: 'سارة',
        lastName: 'أحمد',
        phoneNumber: '+966501234567',
        avatarUrl: 'https://example.com/avatar.jpg',
      );
      
      expect(request.email, equals('<EMAIL>'));
      expect(request.firstName, equals('سارة'));
      expect(request.lastName, equals('أحمد'));
      expect(request.phoneNumber, equals('+966501234567'));
      expect(request.avatarUrl, equals('https://example.com/avatar.jpg'));
    });
    
    test('should parse from JSON correctly', () {
      final request = CreateCarnowUserRequest.fromJson(validRequestJson);
      
      expect(request.email, equals('<EMAIL>'));
      expect(request.firstName, equals('سارة'));
      expect(request.lastName, equals('أحمد'));
      expect(request.phoneNumber, equals('+966501234567'));
      expect(request.avatarUrl, equals('https://example.com/avatar.jpg'));
    });
    
    test('should convert to JSON correctly', () {
      final request = CreateCarnowUserRequest.fromJson(validRequestJson);
      final json = request.toJson();
      
      expect(json['email'], equals('<EMAIL>'));
      expect(json['first_name'], equals('سارة'));
      expect(json['last_name'], equals('أحمد'));
      expect(json['phone_number'], equals('+966501234567'));
      expect(json['avatar_url'], equals('https://example.com/avatar.jpg'));
    });
  });
  
  group('UpdateCarnowUserRequest Tests', () {
    test('should create update request with partial fields', () {
      final request = UpdateCarnowUserRequest(
        firstName: 'محمد',
        phoneNumber: '+966509876543',
      );
      
      expect(request.firstName, equals('محمد'));
      expect(request.phoneNumber, equals('+966509876543'));
      expect(request.lastName, isNull);
      expect(request.avatarUrl, isNull);
    });
    
    test('should parse from JSON with partial fields', () {
      final json = {
        'first_name': 'محمد',
        'phone_number': '+966509876543',
      };
      
      final request = UpdateCarnowUserRequest.fromJson(json);
      
      expect(request.firstName, equals('محمد'));
      expect(request.phoneNumber, equals('+966509876543'));
      expect(request.lastName, isNull);
      expect(request.avatarUrl, isNull);
    });
    
    test('should convert to JSON correctly', () {
      final request = UpdateCarnowUserRequest(
        firstName: 'محمد',
        phoneNumber: '+966509876543',
      );

      final json = request.toJson();

      expect(json['first_name'], equals('محمد'));
      expect(json['phone_number'], equals('+966509876543'));
      // Freezed includes null values in JSON, so we check for null instead
      expect(json['last_name'], isNull);
      expect(json['avatar_url'], isNull);
    });
  });
}
