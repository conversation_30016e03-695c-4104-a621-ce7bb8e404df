// Comprehensive tests for OrderModel
// اختبارات شاملة لنموذج OrderModel

import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/core/models/order_model.dart';
import 'package:carnow/core/models/order_item_model.dart';
import 'package:carnow/core/models/enums.dart';

void main() {
  group('OrderModel Tests', () {
    late Map<String, dynamic> validOrderJson;
    late OrderModel validOrder;
    late List<OrderItemModel> orderItems;
    
    setUp(() {
      orderItems = [
        OrderItemModel(
          id: 1,
          productId: '101',
          quantity: 2,
          unitPrice: 150.0,
        ),
        OrderItemModel(
          id: 2,
          productId: '102',
          quantity: 1,
          unitPrice: 250.0,
        ),
      ];
      
      validOrderJson = {
        'id': 1,
        'buyerId': 123,
        'totalAmount': 550.0,
        'currency': 'LYD',
        'status': 'pending',
        'shippingAddress': 'طرابلس، ليبيا - شارع الجمهورية',
        'createdAt': '2024-01-01T10:00:00.000Z',
        'updatedAt': '2024-01-01T10:30:00.000Z',
        'items': [
          {
            'id': 1,
            'productId': 101,
            'quantity': 2,
            'unitPrice': 150.0,
            'totalPrice': 300.0,
          },
          {
            'id': 2,
            'productId': 102,
            'quantity': 1,
            'unitPrice': 250.0,
            'totalPrice': 250.0,
          },
        ],
        'trackingNumber': 'TRK123456789',
      };
      
      validOrder = OrderModel(
        id: 1,
        buyerId: 123,
        totalAmount: 550.0,
        currency: 'LYD',
        status: OrderStatus.pending,
        shippingAddress: 'طرابلس، ليبيا - شارع الجمهورية',
        createdAt: DateTime.parse('2024-01-01T10:00:00.000Z'),
        updatedAt: DateTime.parse('2024-01-01T10:30:00.000Z'),
        items: orderItems,
        trackingNumber: 'TRK123456789',
      );
    });
    
    group('OrderModel Creation', () {
      test('should create OrderModel with minimal fields', () {
        final order = OrderModel();
        
        expect(order.id, isNull);
        expect(order.buyerId, isNull);
        expect(order.totalAmount, isNull);
        expect(order.currency, equals('LYD'));
        expect(order.status, isNull);
        expect(order.shippingAddress, isNull);
        expect(order.createdAt, isNull);
        expect(order.updatedAt, isNull);
        expect(order.items, isNull);
        expect(order.trackingNumber, isNull);
      });
      
      test('should create OrderModel with all fields', () {
        expect(validOrder.id, equals(1));
        expect(validOrder.buyerId, equals(123));
        expect(validOrder.totalAmount, equals(550.0));
        expect(validOrder.currency, equals('LYD'));
        expect(validOrder.status, equals(OrderStatus.pending));
        expect(validOrder.shippingAddress, equals('طرابلس، ليبيا - شارع الجمهورية'));
        expect(validOrder.trackingNumber, equals('TRK123456789'));
        expect(validOrder.items, hasLength(2));
      });
      
      test('should create OrderModel with custom currency', () {
        final order = OrderModel(
          id: 1,
          currency: 'USD',
        );
        
        expect(order.currency, equals('USD'));
      });
    });
    
    group('JSON Serialization', () {
      test('should parse OrderModel from valid JSON', () {
        final order = OrderModel.fromJson(validOrderJson);
        
        expect(order.id, equals(1));
        expect(order.buyerId, equals(123));
        expect(order.totalAmount, equals(550.0));
        expect(order.currency, equals('LYD'));
        expect(order.status, equals(OrderStatus.pending));
        expect(order.shippingAddress, equals('طرابلس، ليبيا - شارع الجمهورية'));
        expect(order.trackingNumber, equals('TRK123456789'));
        expect(order.items, hasLength(2));
      });
      
      test('should convert OrderModel to JSON correctly', () {
        final json = validOrder.toJson();
        
        expect(json['id'], equals(1));
        expect(json['buyerId'], equals(123));
        expect(json['totalAmount'], equals(550.0));
        expect(json['currency'], equals('LYD'));
        expect(json['status'], equals('pending'));
        expect(json['shippingAddress'], equals('طرابلس، ليبيا - شارع الجمهورية'));
        expect(json['trackingNumber'], equals('TRK123456789'));
        expect(json['items'], hasLength(2));
      });
      
      test('should handle minimal JSON', () {
        final minimalJson = <String, dynamic>{};
        
        final order = OrderModel.fromJson(minimalJson);
        
        expect(order.id, isNull);
        expect(order.buyerId, isNull);
        expect(order.totalAmount, isNull);
        expect(order.currency, equals('LYD'));
        expect(order.status, isNull);
        expect(order.items, isNull);
      });
      
      test('should handle null values in JSON', () {
        final jsonWithNulls = {
          'id': null,
          'buyerId': null,
          'totalAmount': null,
          'currency': 'LYD',
          'status': null,
          'shippingAddress': null,
          'createdAt': null,
          'updatedAt': null,
          'items': null,
          'trackingNumber': null,
        };
        
        final order = OrderModel.fromJson(jsonWithNulls);
        
        expect(order.id, isNull);
        expect(order.buyerId, isNull);
        expect(order.totalAmount, isNull);
        expect(order.currency, equals('LYD'));
        expect(order.status, isNull);
        expect(order.items, isNull);
      });
    });
    
    group('Order Status Handling', () {
      test('should handle all order statuses', () {
        final statuses = [
          OrderStatus.pending,
          OrderStatus.confirmed,
          OrderStatus.processing,
          OrderStatus.shipped,
          OrderStatus.delivered,
          OrderStatus.cancelled,
        ];
        
        for (final status in statuses) {
          final order = OrderModel(status: status);
          expect(order.status, equals(status));
        }
      });
      
      test('should parse order status from string', () {
        final testCases = {
          'pending': OrderStatus.pending,
          'confirmed': OrderStatus.confirmed,
          'processing': OrderStatus.processing,
          'shipped': OrderStatus.shipped,
          'delivered': OrderStatus.delivered,
          'cancelled': OrderStatus.cancelled,
        };
        
        testCases.forEach((statusString, expectedStatus) {
          final json = {'status': statusString};
          final order = OrderModel.fromJson(json);
          expect(order.status, equals(expectedStatus));
        });
      });
    });
    
    group('DateTime Handling', () {
      test('should parse DateTime fields correctly', () {
        final order = OrderModel.fromJson(validOrderJson);
        
        expect(order.createdAt, equals(DateTime.parse('2024-01-01T10:00:00.000Z')));
        expect(order.updatedAt, equals(DateTime.parse('2024-01-01T10:30:00.000Z')));
      });
      
      test('should serialize DateTime fields correctly', () {
        final json = validOrder.toJson();
        
        expect(json['createdAt'], equals('2024-01-01T10:00:00.000Z'));
        expect(json['updatedAt'], equals('2024-01-01T10:30:00.000Z'));
      });
      
      test('should handle null DateTime fields', () {
        final order = OrderModel(
          id: 1,
          createdAt: null,
          updatedAt: null,
        );
        
        expect(order.createdAt, isNull);
        expect(order.updatedAt, isNull);
      });
    });
    
    group('Order Items Handling', () {
      test('should handle empty items list', () {
        final order = OrderModel(
          id: 1,
          items: [],
        );
        
        expect(order.items, isEmpty);
      });
      
      test('should handle multiple order items', () {
        final order = OrderModel(
          id: 1,
          items: orderItems,
        );
        
        expect(order.items, hasLength(2));
        expect(order.items![0].productId, equals(101));
        expect(order.items![1].productId, equals(102));
      });
      
      test('should calculate total from items', () {
        // Note: This would be a business logic method if implemented
        final order = OrderModel(
          id: 1,
          items: orderItems,
        );
        
        final calculatedTotal = order.items!
            .fold(0.0, (sum, item) => sum + ((item.unitPrice ?? 0.0) * (item.quantity ?? 0)));
        
        expect(calculatedTotal, equals(550.0));
      });
    });
    
    group('Equality and Comparison', () {
      test('should be equal when all fields match', () {
        final order1 = OrderModel.fromJson(validOrderJson);
        final order2 = OrderModel.fromJson(validOrderJson);
        
        expect(order1, equals(order2));
        expect(order1.hashCode, equals(order2.hashCode));
      });
      
      test('should not be equal when fields differ', () {
        final order1 = OrderModel.fromJson(validOrderJson);
        final differentJson = Map<String, dynamic>.from(validOrderJson);
        differentJson['totalAmount'] = 999.0;
        final order2 = OrderModel.fromJson(differentJson);
        
        expect(order1, isNot(equals(order2)));
      });
    });
    
    group('Edge Cases', () {
      test('should handle very large amounts', () {
        final order = OrderModel(
          totalAmount: 999999999.99,
        );
        
        expect(order.totalAmount, equals(999999999.99));
      });
      
      test('should handle zero amount', () {
        final order = OrderModel(
          totalAmount: 0.0,
        );
        
        expect(order.totalAmount, equals(0.0));
      });
      
      test('should handle negative amount', () {
        final order = OrderModel(
          totalAmount: -100.0,
        );
        
        expect(order.totalAmount, equals(-100.0));
      });
      
      test('should handle very long shipping address', () {
        final longAddress = 'طرابلس، ليبيا - ' * 50;
        final order = OrderModel(
          shippingAddress: longAddress,
        );
        
        expect(order.shippingAddress, equals(longAddress));
      });
      
      test('should handle Arabic characters in address', () {
        final arabicAddress = 'طرابلس، ليبيا - شارع الجمهورية - بناية النصر - الطابق الثالث';
        final order = OrderModel(
          shippingAddress: arabicAddress,
        );
        
        expect(order.shippingAddress, equals(arabicAddress));
      });
    });
  });
}
