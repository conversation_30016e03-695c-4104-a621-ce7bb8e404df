// Enhanced comprehensive tests for CategoryModel
// اختبارات شاملة محسنة لنموذج CategoryModel

import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/core/models/category_model.dart';

void main() {
  group('CategoryModel Tests', () {
    late Map<String, dynamic> testCategoryData;
    late CategoryModel testCategory;

    setUp(() {
      testCategoryData = {
        'id': 'test-category-1',
        'name_ar': 'قطع غيار المحرك',
        'name_en': 'Engine Parts',
        'name_it': 'Parti del Motore',
        'type': 'auto_parts',
        'description_ar': 'قطع غيار خاصة بمحرك السيارة',
        'description_en': 'Car engine spare parts',
        'description_it': 'Ricambi per motore auto',
        'image_url': 'https://example.com/engine-parts.jpg',
        'icon_url': 'https://example.com/engine-icon.svg',
        'parent_id': 'parent-category-1',
        'sort_order': 1,
        'is_active': true,
        'is_featured': true,
        'custom_attributes': {
          'warranty_months': 12,
          'origin_country': 'Germany',
          'quality_grade': 'OEM'
        },
        'allowed_filters': ['brand', 'model', 'year', 'condition'],
        'configuration': {
          'maxProducts': 1000,
          'allowSubcategories': true,
          'requireApproval': false
        },
        'created_at': '2024-01-01T10:00:00.000Z',
        'updated_at': '2024-01-01T10:30:00.000Z',
      };

      testCategory = CategoryModel(
        id: 'test-category-1',
        name: 'قطع غيار المحرك',
        nameEn: 'Engine Parts',
        type: CategoryType.autoParts,
        description: 'قطع غيار خاصة بمحرك السيارة',
        isActive: true,
        sortOrder: 1,
        createdAt: DateTime.parse('2024-01-01T10:00:00.000Z'),
        updatedAt: DateTime.parse('2024-01-01T10:30:00.000Z'),
      );
    });

    group('Creation Tests', () {
      test('should create CategoryModel with all required fields', () {
        final category = CategoryModel(
          id: 'cat_123',
          name: 'قطع غيار',
          nameEn: 'Auto Parts',
          type: CategoryType.autoParts,
        );

        expect(category.id, equals('cat_123'));
        expect(category.name, equals('قطع غيار'));
        expect(category.nameEn, equals('Auto Parts'));
        expect(category.type, equals(CategoryType.autoParts));
        expect(category.sortOrder, equals(0)); // default value
        expect(category.isActive, isTrue); // default value
        expect(category.isFeatured, isFalse); // default value
      });

      test('should create CategoryModel with all optional fields', () {
        final category = CategoryModel(
          id: 'cat_456',
          name: 'إلكترونيات',
          nameEn: 'Electronics',
          nameIt: 'Elettronica',
          type: CategoryType.electronics,
          description: 'أجهزة إلكترونية متنوعة',
          descriptionEn: 'Various electronic devices',
          descriptionIt: 'Vari dispositivi elettronici',
          imageUrl: 'https://example.com/electronics.jpg',
          iconUrl: 'https://example.com/electronics-icon.svg',
          parentId: 'parent_123',
          sortOrder: 5,
          isActive: false,
          isFeatured: true,
          customAttributes: {'warranty': '2 years'},
          allowedFilters: ['brand', 'price'],
          createdAt: DateTime.parse('2024-01-01T10:00:00.000Z'),
          updatedAt: DateTime.parse('2024-01-01T10:30:00.000Z'),
        );

        expect(category.nameIt, equals('Elettronica'));
        expect(category.description, equals('أجهزة إلكترونية متنوعة'));
        expect(category.descriptionEn, equals('Various electronic devices'));
        expect(category.descriptionIt, equals('Vari dispositivi elettronici'));
        expect(category.imageUrl, equals('https://example.com/electronics.jpg'));
        expect(category.iconUrl, equals('https://example.com/electronics-icon.svg'));
        expect(category.parentId, equals('parent_123'));
        expect(category.sortOrder, equals(5));
        expect(category.isActive, isFalse);
        expect(category.isFeatured, isTrue);
        expect(category.customAttributes, isNotNull);
        expect(category.customAttributes!['warranty'], equals('2 years'));
        expect(category.allowedFilters, contains('brand'));
        expect(category.allowedFilters, contains('price'));
      });
    });

    group('JSON Serialization Tests', () {
      test('should parse CategoryModel from valid JSON', () {
        final category = CategoryModel.fromJson(testCategoryData);

        expect(category.id, equals('test-category-1'));
        expect(category.name, equals('قطع غيار المحرك'));
        expect(category.nameEn, equals('Engine Parts'));
        expect(category.nameIt, equals('Parti del Motore'));
        expect(category.type, equals(CategoryType.autoParts));
        expect(category.description, equals('قطع غيار خاصة بمحرك السيارة'));
        expect(category.descriptionEn, equals('Car engine spare parts'));
        expect(category.descriptionIt, equals('Ricambi per motore auto'));
        expect(category.imageUrl, equals('https://example.com/engine-parts.jpg'));
        expect(category.iconUrl, equals('https://example.com/engine-icon.svg'));
        expect(category.parentId, equals('parent-category-1'));
        expect(category.sortOrder, equals(1));
        expect(category.isActive, isTrue);
        expect(category.isFeatured, isTrue);
        expect(category.customAttributes, isNotNull);
        expect(category.allowedFilters, isNotNull);
        expect(category.createdAt, isNotNull);
        expect(category.updatedAt, isNotNull);
      });

      test('should convert CategoryModel to JSON correctly', () {
        final json = testCategory.toJson();

        expect(json['id'], equals('test-category-1'));
        expect(json['name'], equals('قطع غيار المحرك'));
        expect(json['name_en'], equals('Engine Parts'));
        expect(json['type'], equals('auto_parts'));
        expect(json['description'], equals('قطع غيار خاصة بمحرك السيارة'));
        expect(json['sort_order'], equals(1));
        expect(json['is_active'], isTrue);
        expect(json['created_at'], isNotNull);
        expect(json['updated_at'], isNotNull);
      });

      test('should handle minimal JSON with defaults', () {
        final minimalJson = {
          'id': 'minimal_cat',
          'name': 'فئة بسيطة',
          'type': 'other',
        };

        final category = CategoryModel.fromJson(minimalJson);

        expect(category.id, equals('minimal_cat'));
        expect(category.name, equals('فئة بسيطة'));
        expect(category.nameEn, equals('فئة بسيطة')); // falls back to name
        expect(category.type, equals(CategoryType.other));
        expect(category.sortOrder, equals(0));
        expect(category.isActive, isTrue);
        expect(category.isFeatured, isFalse);
        expect(category.description, isNull);
        expect(category.parentId, isNull);
        expect(category.customAttributes, isNull);
        expect(category.allowedFilters, isNull);
      });
    });

    group('Equality and Comparison Tests', () {
      test('should be equal when all fields match', () {
        final category1 = CategoryModel(
          id: 'cat_1',
          name: 'Test Category',
          nameEn: 'Test Category',
          type: CategoryType.autoParts,
        );

        final category2 = CategoryModel(
          id: 'cat_1',
          name: 'Test Category',
          nameEn: 'Test Category',
          type: CategoryType.autoParts,
        );

        expect(category1, equals(category2));
        expect(category1.hashCode, equals(category2.hashCode));
      });

      test('should not be equal when fields differ', () {
        final category1 = CategoryModel(
          id: 'cat_1',
          name: 'Test Category',
          nameEn: 'Test Category',
          type: CategoryType.autoParts,
        );

        final category2 = CategoryModel(
          id: 'cat_2',
          name: 'Test Category',
          nameEn: 'Test Category',
          type: CategoryType.autoParts,
        );

        expect(category1, isNot(equals(category2)));
        expect(category1.hashCode, isNot(equals(category2.hashCode)));
      });
    });

    group('CopyWith Tests', () {
      test('should create copy with updated fields', () {
        final originalCategory = CategoryModel(
          id: 'original_id',
          name: 'Original Name',
          nameEn: 'Original Name EN',
          type: CategoryType.autoParts,
          isActive: true,
        );

        final updatedCategory = originalCategory.copyWith(
          name: 'Updated Name',
          isActive: false,
          sortOrder: 10,
        );

        expect(updatedCategory.id, equals('original_id')); // unchanged
        expect(updatedCategory.name, equals('Updated Name')); // changed
        expect(updatedCategory.nameEn, equals('Original Name EN')); // unchanged
        expect(updatedCategory.type, equals(CategoryType.autoParts)); // unchanged
        expect(updatedCategory.isActive, isFalse); // changed
        expect(updatedCategory.sortOrder, equals(10)); // changed
      });

      test('should preserve original when no changes provided', () {
        final originalCategory = CategoryModel(
          id: 'test_id',
          name: 'Test Name',
          nameEn: 'Test Name EN',
          type: CategoryType.electronics,
        );

        final copiedCategory = originalCategory.copyWith();

        expect(copiedCategory, equals(originalCategory));
      });
    });

    group('CategoryType Tests', () {
      test('should convert from string correctly', () {
        expect(CategoryType.fromString('auto_parts'), equals(CategoryType.autoParts));
        expect(CategoryType.fromString('electronics'), equals(CategoryType.electronics));
        expect(CategoryType.fromString('clothing'), equals(CategoryType.clothing));
        expect(CategoryType.fromString('home'), equals(CategoryType.home));
        expect(CategoryType.fromString('sports'), equals(CategoryType.sports));
        expect(CategoryType.fromString('beauty'), equals(CategoryType.beauty));
        expect(CategoryType.fromString('books'), equals(CategoryType.books));
        expect(CategoryType.fromString('toys'), equals(CategoryType.toys));
        expect(CategoryType.fromString('food'), equals(CategoryType.food));
        expect(CategoryType.fromString('other'), equals(CategoryType.other));
        expect(CategoryType.fromString('unknown'), equals(CategoryType.other)); // fallback
      });

      test('should have correct display properties', () {
        expect(CategoryType.autoParts.value, equals('auto_parts'));
        expect(CategoryType.autoParts.displayName, equals('قطع غيار السيارات'));
        expect(CategoryType.autoParts.emoji, equals('🚗'));

        expect(CategoryType.electronics.value, equals('electronics'));
        expect(CategoryType.electronics.displayName, equals('الإلكترونيات'));
        expect(CategoryType.electronics.emoji, equals('📱'));

        expect(CategoryType.clothing.value, equals('clothing'));
        expect(CategoryType.clothing.displayName, equals('الملابس'));
        expect(CategoryType.clothing.emoji, equals('👕'));
      });

      test('should return correct active status', () {
        expect(CategoryType.autoParts.isActive, isTrue);
        expect(CategoryType.electronics.isActive, isFalse);
        expect(CategoryType.clothing.isActive, isFalse);
        expect(CategoryType.home.isActive, isFalse);
        expect(CategoryType.sports.isActive, isFalse);
        expect(CategoryType.beauty.isActive, isFalse);
        expect(CategoryType.books.isActive, isFalse);
        expect(CategoryType.toys.isActive, isFalse);
        expect(CategoryType.food.isActive, isFalse);
        expect(CategoryType.other.isActive, isFalse);
      });

      test('should return correct supported filters', () {
        final autoPartsFilters = CategoryType.autoParts.supportedFilters;
        expect(autoPartsFilters, contains('car_brand'));
        expect(autoPartsFilters, contains('car_model'));
        expect(autoPartsFilters, contains('car_year'));
        expect(autoPartsFilters, contains('part_type'));
        expect(autoPartsFilters, contains('condition'));
        expect(autoPartsFilters, contains('warranty'));

        final electronicsFilters = CategoryType.electronics.supportedFilters;
        expect(electronicsFilters, contains('brand'));
        expect(electronicsFilters, contains('screen_size'));
        expect(electronicsFilters, contains('storage'));
        expect(electronicsFilters, contains('ram'));
        expect(electronicsFilters, contains('connectivity'));
        expect(electronicsFilters, contains('warranty'));
      });
    });
  });
}
