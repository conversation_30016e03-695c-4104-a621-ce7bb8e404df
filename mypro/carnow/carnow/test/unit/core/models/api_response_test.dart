import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/core/models/api_response.dart';

void main() {
  group('ApiResponse Tests', () {
    test('should create success response correctly', () {
      const data = {'name': 'Test', 'value': 123};
      final response = ApiResponse.success(data);

      expect(response.isSuccess, isTrue);
      expect(response.isFailure, isFalse);
      expect(response.data, equals(data));
      expect(response.error, isNull);
      expect(response.message, isNull);
    });

    test('should create error response correctly', () {
      const error = 'Network error';
      final response = ApiResponse<Map<String, dynamic>>.error(error);

      expect(response.isSuccess, isFalse);
      expect(response.isFailure, isTrue);
      expect(response.data, isNull);
      expect(response.error, equals(error));
      expect(response.message, isNull);
    });

    test('should create success response with message', () {
      const data = 'Success data';
      const message = 'Operation completed';
      final response = ApiResponse.success(data, message: message);

      expect(response.isSuccess, isTrue);
      expect(response.data, equals(data));
      expect(response.message, equals(message));
      expect(response.error, isNull);
    });

    test('should create error response with custom message', () {
      const error = 'Internal error';
      const message = 'Please try again later';
      final response = ApiResponse<String>.error(error, message: message);

      expect(response.isFailure, isTrue);
      expect(response.error, equals(error));
      expect(response.message, equals(message));
      expect(response.data, isNull);
    });

    test('should handle null data in success response', () {
      final response = ApiResponse<String?>.success(null);

      expect(response.isSuccess, isTrue);
      expect(response.data, isNull);
      expect(response.error, isNull);
    });

    test('should handle empty string error', () {
      const error = '';
      final response = ApiResponse<int>.error(error);

      expect(response.isFailure, isTrue);
      expect(response.error, equals(error));
    });

    test('should create loading state correctly', () {
      final response = ApiResponse<String>.loading();

      expect(response.isLoading, isTrue);
      expect(response.isSuccess, isFalse);
      expect(response.isFailure, isFalse);
      expect(response.data, isNull);
      expect(response.error, isNull);
    });

    test('should support different data types', () {
      // Test with List
      final listResponse = ApiResponse.success([1, 2, 3]);
      expect(listResponse.data, equals([1, 2, 3]));

      // Test with Map
      final mapResponse = ApiResponse.success({'key': 'value'});
      expect(mapResponse.data, equals({'key': 'value'}));

      // Test with String
      final stringResponse = ApiResponse.success('test string');
      expect(stringResponse.data, equals('test string'));

      // Test with int
      final intResponse = ApiResponse.success(42);
      expect(intResponse.data, equals(42));

      // Test with bool
      final boolResponse = ApiResponse.success(true);
      expect(boolResponse.data, equals(true));
    });

    test('should handle complex nested data', () {
      final complexData = {
        'users': [
          {'id': 1, 'name': 'John', 'active': true},
          {'id': 2, 'name': 'Jane', 'active': false},
        ],
        'meta': {'total': 2, 'page': 1, 'hasMore': false},
      };

      final response = ApiResponse.success(complexData);

      expect(response.isSuccess, isTrue);
      expect(response.data, equals(complexData));
      expect(response.data?['users'], isA<List>());
      expect(response.data?['meta'], isA<Map<String, dynamic>>());
    });

    test('should create response with all parameters', () {
      const data = {'result': 'success'};
      const message = 'Data retrieved successfully';
      final response = ApiResponse.success(data, message: message);

      expect(response.isSuccess, isTrue);
      expect(response.data, equals(data));
      expect(response.message, equals(message));
      expect(response.error, isNull);
    });

    test('should maintain immutability', () {
      final originalData = {'count': 1};
      final response = ApiResponse.success(originalData);

      // Modifying original data shouldn't affect response
      originalData['count'] = 2;

      // Note: This test depends on implementation details
      // In a real scenario, you might want to use immutable data structures
      expect(response.data?['count'], equals(2)); // Since Map is mutable
    });

    test('should work with custom classes', () {
      final testObject = TestUser(id: 1, name: 'Test User');
      final response = ApiResponse.success(testObject);

      expect(response.isSuccess, isTrue);
      expect(response.data?.id, equals(1));
      expect(response.data?.name, equals('Test User'));
    });

    test('error response should not have data', () {
      final response = ApiResponse<TestUser>.error('User not found');

      expect(response.isFailure, isTrue);
      expect(response.data, isNull);
      expect(response.error, equals('User not found'));
    });

    test('should handle unicode and special characters', () {
      const arabicData = 'بيانات تجريبية باللغة العربية';
      final response = ApiResponse.success(arabicData);

      expect(response.data, equals(arabicData));
      expect(response.toString(), contains(arabicData));
    });

    test('should handle very long error messages', () {
      final longError = 'Error: ${'Very long error message. ' * 100}';
      final response = ApiResponse<String>.error(longError);

      expect(response.error, equals(longError));
      expect(response.toString(), contains('ApiResponse.error'));
    });

    test('should support chaining operations based on state', () {
      final successResponse = ApiResponse.success('data');
      final errorResponse = ApiResponse<String>.error('error');
      final loadingResponse = ApiResponse<String>.loading();

      String result = '';

      if (successResponse.isSuccess) {
        result = 'success';
      } else if (successResponse.isFailure) {
        result = 'failure';
      } else if (successResponse.isLoading) {
        result = 'loading';
      }

      expect(result, equals('success'));
      expect(errorResponse.isFailure, isTrue);
      expect(loadingResponse.isLoading, isTrue);
    });

    test('should create loading state with message', () {
      const loadingMessage = 'Loading data...';
      final response = ApiResponse<String>.loading(message: loadingMessage);

      expect(response.isLoading, isTrue);
      expect(response.message, equals(loadingMessage));
    });

    test('should handle toString for all states', () {
      final successResponse = ApiResponse.success('data', message: 'Success');
      final errorResponse = ApiResponse<String>.error('error', message: 'Error occurred');
      final loadingResponse = ApiResponse<String>.loading(message: 'Loading...');

      expect(successResponse.toString(), contains('ApiResponse.success'));
      expect(errorResponse.toString(), contains('ApiResponse.error'));
      expect(loadingResponse.toString(), contains('ApiResponse.loading'));
    });

    test('should handle unicode and special characters', () {
      const unicodeData = 'Test data emoji Special chars: @#\$%^&*()';
      final response = ApiResponse.success(unicodeData);

      expect(response.isSuccess, isTrue);
      expect(response.data, equals(unicodeData));
    });

    test('should handle very long error messages', () {
      final buffer = StringBuffer('Error: ');
      for (int i = 0; i < 100; i++) {
        buffer.write('Very long error message. ');
      }
      final longError = buffer.toString();
      final response = ApiResponse<String>.error(longError);

      expect(response.isFailure, isTrue);
      expect(response.error, equals(longError));
      expect(response.error!.length, greaterThan(1000));
    });

    test('should support chaining operations based on state', () {
      final successResponse = ApiResponse.success('test data');
      final errorResponse = ApiResponse<String>.error('test error');

      // Test success flow
      String successResult = 'default';
      if (successResponse.isSuccess) {
        successResult = successResponse.data ?? 'no data';
      }
      expect(successResult, equals('test data'));

      // Test error flow
      String errorResult = 'default';
      if (errorResponse.isFailure) {
        errorResult = errorResponse.error ?? 'no error';
      }
      expect(errorResult, equals('test error'));
    });
  });
}

// Helper class for testing
class TestUser {
  TestUser({required this.id, required this.name});
  final int id;
  final String name;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TestUser &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name;

  @override
  int get hashCode => id.hashCode ^ name.hashCode;
}
