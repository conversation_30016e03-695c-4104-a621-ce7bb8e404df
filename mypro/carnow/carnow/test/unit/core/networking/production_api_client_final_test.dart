import 'package:flutter_test/flutter_test.dart';
import 'package:riverpod/riverpod.dart';
import 'package:carnow/core/networking/production_api_client.dart';

// Simple adapter to convert ProviderContainer to Ref<Object?>
class RefAdapter implements Ref<Object?> {
  final ProviderContainer _container;
  
  RefAdapter(this._container);
  
  @override
  T read<T>(ProviderListenable<T> provider) => _container.read(provider);
  
  @override
  T watch<T>(ProviderListenable<T> provider) => _container.read(provider);
  
  @override
  ProviderSubscription<T> listen<T>(ProviderListenable<T> provider, void Function(T? previous, T next) listener, {bool fireImmediately = false, void Function(Object error, StackTrace stackTrace)? onError}) {
    return _container.listen(provider, listener, fireImmediately: fireImmediately, onError: onError);
  }
  
  @override
  void invalidate(ProviderOrFamily provider) {}
  
  @override
  void invalidateSelf() {}
  
  @override
  T refresh<T>(Refreshable<T> provider) => _container.refresh(provider);
  
  @override
  void dispose() {}
  
  @override
  void state(ProviderOrFamily provider) {}
  
  @override
  ProviderContainer get container => _container;
  
  @override
  bool exists(ProviderBase<Object?> provider) => _container.exists(provider);
  
  @override
  KeepAliveLink keepAlive() => throw UnimplementedError();
  
  @override
  void listenSelf(void Function(Object? previous, Object next) listener, {void Function(Object error, StackTrace stackTrace)? onError}) {}
  
  @override
  void notifyListeners() {}
  
  @override
  void onAddListener(void Function() cb) {}
  
  @override
  void onCancel(void Function() cb) {}
  
  @override
  void onDispose(void Function() cb) {}
  
  @override
  void onRemoveListener(void Function() cb) {}
  
  @override
  void onResume(void Function() cb) {}
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('ProductionApiClient Tests', () {
    late ProviderContainer container;
    late ProductionApiClient client;
    
    setUp(() {
      container = ProviderContainer();
      client = ProductionApiClient(
        ref: RefAdapter(container),
        baseUrl: 'https://jsonplaceholder.typicode.com',
        timeout: const Duration(seconds: 10),
      );
    });

    tearDown(() {
      client.dispose();
      container.dispose();
    });

    test('ProductionApiClient can be instantiated', () {
      expect(client, isA<ProductionApiClient>());
    });

    test('GET request works with real API', () async {
      final posts = await client.get(
        '/posts',
        fromJson: (json) => List<Map<String, dynamic>>.from(json as List).map((e) => e).toList(),
      );
      
      expect(posts, isA<List>());
      expect(posts.length, greaterThan(0));
    });

    test('POST request works with real API', () async {
      final post = await client.post(
        '/posts',
        data: {
          'title': 'Test Post',
          'body': 'Test body content',
          'userId': 1,
        },
        fromJson: (json) => json,
      );
      
      expect(post, isA<Map<String, dynamic>>());
      expect(post['title'], 'Test Post');
    });

    test('PUT request works with real API', () async {
      final post = await client.put(
        '/posts/1',
        data: {
          'id': 1,
          'title': 'Updated Post',
          'body': 'Updated body content',
          'userId': 1,
        },
        fromJson: (json) => json,
      );
      
      expect(post, isA<Map<String, dynamic>>());
      expect(post['title'], 'Updated Post');
    });

    test('DELETE request works with real API', () async {
      await client.delete(
        '/posts/1',
        fromJson: (json) => json,
      );
      
      expect(true, isTrue);
    });

    test('handles 404 errors gracefully', () async {
      try {
        await client.get(
          '/posts/999999',
          fromJson: (json) => json,
        );
        fail('Expected exception');
      } catch (e) {
        expect(e.toString(), anyOf(contains('404'), contains('Not Found')));
      }
    });

    test('has uploadFile method', () {
      expect(client.uploadFile, isA<Function>());
    });

    test('has cancelRequests method', () {
      expect(client.cancelRequests, isA<Function>());
    });

    test('has dispose method', () {
      expect(client.dispose, isA<Function>());
    });
  });
}
