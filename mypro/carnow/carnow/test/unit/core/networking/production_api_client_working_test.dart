import 'package:flutter_test/flutter_test.dart';
import 'package:riverpod/riverpod.dart';
import 'package:carnow/core/networking/production_api_client.dart';

// Simple adapter to convert ProviderContainer to Ref<Object?>
class RefAdapter implements Ref<Object?> {
  final ProviderContainer _container;
  
  RefAdapter(this._container);
  
  @override
  T read<T>(ProviderListenable<T> provider) => _container.read(provider);
  
  @override
  T watch<T>(ProviderListenable<T> provider) => _container.read(provider);
  
  @override
  ProviderSubscription<T> listen<T>(ProviderListenable<T> provider, void Function(T? previous, T next) listener, {bool fireImmediately = false, void Function(Object error, StackTrace stackTrace)? onError}) {
    return _container.listen(provider, listener, fireImmediately: fireImmediately, onError: onError);
  }
  
  @override
  void invalidate(ProviderOrFamily provider) {}
  
  @override
  void invalidateSelf() {}
  
  @override
  T refresh<T>(Refreshable<T> provider) => _container.refresh(provider);
  
  void dispose() {}
  
  void state(ProviderOrFamily provider) {}
  
  @override
  ProviderContainer get container => _container;
  
  @override
  bool exists(ProviderBase<Object?> provider) => _container.exists(provider);
  
  @override
  KeepAliveLink keepAlive() => throw UnimplementedError();
  
  @override
  void listenSelf(void Function(Object? previous, Object next) listener, {void Function(Object error, StackTrace stackTrace)? onError}) {}
  
  @override
  void notifyListeners() {}
  
  @override
  void onAddListener(void Function() cb) {}
  
  @override
  void onCancel(void Function() cb) {}
  
  @override
  void onDispose(void Function() cb) {}
  
  @override
  void onRemoveListener(void Function() cb) {}
  
  @override
  void onResume(void Function() cb) {}
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('ProductionApiClient Core Tests', () {
    late ProviderContainer container;
    late ProductionApiClient client;
    
    setUp(() {
      container = ProviderContainer();
      client = ProductionApiClient(
        ref: RefAdapter(container),
        baseUrl: 'https://jsonplaceholder.typicode.com',
        timeout: const Duration(seconds: 10),
      );
    });

    tearDown(() {
      client.dispose();
      container.dispose();
    });

    test('creates instance successfully', () {
      expect(client, isNotNull);
    });

    test('has all required methods', () {
      expect(client.get, isA<Function>());
      expect(client.post, isA<Function>());
      expect(client.put, isA<Function>());
      expect(client.delete, isA<Function>());
      expect(client.uploadFile, isA<Function>());
      expect(client.cancelRequests, isA<Function>());
      expect(client.dispose, isA<Function>());
    });

    group('HTTP Operations', () {
      test('GET request works with jsonplaceholder', () async {
        final result = await client.get<Map<String, dynamic>>(
          '/posts/1',
          fromJson: (json) => json,
        );
        
        expect(result, isNotNull);
        expect(result['id'], equals(1));
        expect(result['userId'], isNotNull);
        expect(result['title'], isNotNull);
      });

      test('POST request creates resource', () async {
        final postData = {
          'title': 'Test Post',
          'body': 'Test body content',
          'userId': 1,
        };
        
        final result = await client.post<Map<String, dynamic>>(
          '/posts',
          data: postData,
          fromJson: (json) => json,
        );
        
        expect(result, isNotNull);
        expect(result['id'], isNotNull);
        expect(result['title'], equals('Test Post'));
      });

      test('PUT request updates resource', () async {
        final updateData = {
          'id': 1,
          'title': 'Updated Title',
          'body': 'Updated body',
          'userId': 1,
        };
        
        final result = await client.put<Map<String, dynamic>>(
          '/posts/1',
          data: updateData,
          fromJson: (json) => json,
        );
        
        expect(result, isNotNull);
        expect(result['title'], equals('Updated Title'));
      });

      test('DELETE request works', () async {
        final result = await client.delete<Map<String, dynamic>>(
          '/posts/1',
          fromJson: (json) => json,
        );
        
        expect(result, isNotNull);
      });

      test('GET request with query parameters', () async {
        final result = await client.get<List<dynamic>>(
          '/posts',
          queryParameters: {'userId': '1'},
          fromJson: (json) => json as List<dynamic>,
        );
        
        expect(result, isNotNull);
        expect(result, isA<List>());
        expect(result.length, greaterThan(0));
      });
    });

    group('Error Handling', () {
      test('handles 404 errors gracefully', () async {
        try {
          await client.get<Map<String, dynamic>>(
            '/posts/999999',
            fromJson: (json) => json,
          );
          fail('Expected exception');
        } catch (e) {
          expect(e.toString(), anyOf(contains('404'), contains('Not Found')));
        }
      });

      test('handles network timeout', () async {
        final timeoutClient = ProductionApiClient(
          ref: RefAdapter(container),
          baseUrl: 'https://httpstat.us/200?sleep=2000',
          timeout: const Duration(milliseconds: 100),
        );
        
        try {
          await timeoutClient.get<Map<String, dynamic>>(
            '/',
            fromJson: (json) => json,
          );
          fail('Expected timeout exception');
        } catch (e) {
          expect(e.toString(), contains('timeout'));
        }
        
        timeoutClient.dispose();
      });
    });

    group('Cancellation', () {
      test('can cancel requests', () async {
        expect(() => client.cancelRequests(), returnsNormally);
      });
    });

    group('File Upload', () {
      test('has uploadFile method', () {
        expect(client.uploadFile, isA<Function>());
      });
    });
  });
}
