import 'package:flutter_test/flutter_test.dart';
import 'package:carnow/core/auth/deep_link_handler.dart';

void main() {
  group('DeepLinkHandler', () {
    late DeepLinkHandler handler;

    setUp(() {
      handler = DeepLinkHandler();
    });

    group('OAuth Deep Link Handling', () {
      test('should handle primary OAuth callback deep link', () async {
        const deepLink =
            'io.supabase.carnow://login-callback/?code=abc123&state=xyz';

        final result = await handler.handleOAuthCallback(deepLink);

        expect(result.isSuccess, isTrue);
        expect(result.authCode, equals('abc123'));
        expect(result.state, equals('xyz'));
        expect(result.error, isNull);
      });

      test('should handle fallback OAuth callback deep link', () async {
        const deepLink =
            'io.supabase.carnow://auth-callback/?code=def456&state=abc';

        final result = await handler.handleOAuthCallback(deepLink);

        expect(result.isSuccess, isTrue);
        expect(result.authCode, equals('def456'));
        expect(result.state, equals('abc'));
        expect(result.error, isNull);
      });

      test('should handle OAuth error in deep link', () async {
        const deepLink =
            'io.supabase.carnow://login-callback/?error=access_denied&error_description=User%20denied%20access';

        final result = await handler.handleOAuthCallback(deepLink);

        expect(result.isSuccess, isFalse);
        expect(result.authCode, isNull);
        expect(result.error, contains('access_denied'));
        expect(result.error, contains('User denied access'));
      });

      test('should reject invalid deep link schemes', () async {
        const deepLink = 'invalid://scheme/?code=abc123';

        final result = await handler.handleOAuthCallback(deepLink);

        expect(result.isSuccess, isFalse);
        expect(result.error, contains('Invalid deep link scheme'));
      });

      test('should reject deep links without required parameters', () async {
        const deepLink = 'io.supabase.carnow://login-callback/';

        final result = await handler.handleOAuthCallback(deepLink);

        expect(result.isSuccess, isFalse);
        expect(result.error, contains('Missing required parameters'));
      });
    });

    group('Deep Link Validation', () {
      test('should validate correct deep link formats', () {
        expect(
          handler.isValidOAuthDeepLink(
            'io.supabase.carnow://login-callback/?code=abc',
          ),
          isTrue,
        );
        expect(
          handler.isValidOAuthDeepLink(
            'io.supabase.carnow://auth-callback/?code=abc',
          ),
          isTrue,
        );
        expect(
          handler.isValidOAuthDeepLink(
            'io.supabase.carnow://login-callback/?access_token=xyz',
          ),
          isTrue,
        );
      });

      test('should reject invalid deep link formats', () {
        expect(handler.isValidOAuthDeepLink(''), isFalse);
        expect(handler.isValidOAuthDeepLink('http://localhost:3000'), isFalse);
        expect(handler.isValidOAuthDeepLink('invalid://scheme'), isFalse);
        expect(
          handler.isValidOAuthDeepLink('io.supabase.carnow://invalid-host'),
          isFalse,
        );
      });
    });

    group('URL Parameter Extraction', () {
      test('should extract authorization code correctly', () {
        const url =
            'io.supabase.carnow://login-callback/?code=abc123&state=xyz&other=param';
        final code = handler.extractAuthCode(url);
        expect(code, equals('abc123'));
      });

      test('should extract access token correctly', () {
        const url =
            'io.supabase.carnow://login-callback/?access_token=xyz789&token_type=bearer';
        final token = handler.extractAccessToken(url);
        expect(token, equals('xyz789'));
      });

      test('should extract state parameter correctly', () {
        const url =
            'io.supabase.carnow://login-callback/?code=abc&state=random_state_123';
        final state = handler.extractState(url);
        expect(state, equals('random_state_123'));
      });

      test('should extract error information correctly', () {
        const url =
            'io.supabase.carnow://login-callback/?error=access_denied&error_description=User%20cancelled';
        final error = handler.extractError(url);
        expect(error, contains('access_denied'));
        expect(error, contains('User cancelled'));
      });

      test('should return null for missing parameters', () {
        const url = 'io.supabase.carnow://login-callback/';
        expect(handler.extractAuthCode(url), isNull);
        expect(handler.extractAccessToken(url), isNull);
        expect(handler.extractState(url), isNull);
        expect(handler.extractError(url), isNull);
      });
    });
  });
}
