// Coverage Analysis Tool for CarNow Stage 2 Testing Enhancement
// أداة تحليل التغطية للمرحلة الثانية من تحسين الاختبارات

import 'dart:io';
import 'dart:convert';

class CoverageAnalyzer {
  static const String coverageFile = 'coverage/lcov.info';
  static const double targetCoverage = 95.0;
  
  /// Analyze coverage and generate comprehensive report
  static Future<CoverageReport> analyzeCoverage() async {
    final file = File(coverageFile);
    if (!file.existsSync()) {
      throw Exception('Coverage file not found. Run: flutter test --coverage');
    }
    
    final lines = await file.readAsLines();
    final report = CoverageReport();
    
    String? currentFile;
    int totalLines = 0;
    int coveredLines = 0;
    
    for (final line in lines) {
      if (line.startsWith('SF:')) {
        // Save previous file data
        if (currentFile != null) {
          final coverage = totalLines > 0 ? (coveredLines / totalLines) * 100 : 0.0;
          report.addFile(currentFile, coverage, totalLines, coveredLines);
        }
        
        // Start new file
        currentFile = line.substring(3);
        totalLines = 0;
        coveredLines = 0;
      } else if (line.startsWith('DA:')) {
        // Line coverage data
        final parts = line.substring(3).split(',');
        if (parts.length >= 2) {
          totalLines++;
          if (int.parse(parts[1]) > 0) {
            coveredLines++;
          }
        }
      } else if (line == 'end_of_record' && currentFile != null) {
        // End of current file
        final coverage = totalLines > 0 ? (coveredLines / totalLines) * 100 : 0.0;
        report.addFile(currentFile, coverage, totalLines, coveredLines);
        currentFile = null;
      }
    }
    
    return report;
  }
  
  /// Generate missing tests for uncovered files
  static Future<List<String>> generateMissingTests(CoverageReport report) async {
    final missingTests = <String>[];
    
    for (final file in report.files) {
      if (file.coverage < targetCoverage) {
        final testFile = _getTestFilePath(file.path);
        if (!File(testFile).existsSync()) {
          missingTests.add(testFile);
        }
      }
    }
    
    return missingTests;
  }
  
  /// Get test file path for a source file
  static String _getTestFilePath(String sourcePath) {
    // Convert lib/features/auth/services/auth_service.dart
    // to test/unit/features/auth/services/auth_service_test.dart
    
    if (sourcePath.startsWith('lib/')) {
      final relativePath = sourcePath.substring(4); // Remove 'lib/'
      final pathParts = relativePath.split('/');
      final fileName = pathParts.last;
      final fileNameWithoutExt = fileName.split('.').first;
      
      pathParts[pathParts.length - 1] = '${fileNameWithoutExt}_test.dart';
      
      return 'test/unit/${pathParts.join('/')}';
    }
    
    return 'test/unit/${sourcePath}_test.dart';
  }
  
  /// Generate test template for a file
  static String generateTestTemplate(String filePath) {
    final className = _extractClassName(filePath);
    final importPath = filePath.replaceFirst('lib/', 'package:carnow/');
    
    return '''
// Generated test for $filePath
// اختبار مولد لـ $filePath

import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import '$importPath';

void main() {
  group('$className Tests', () {
    late $className ${className.toLowerCase()};
    
    setUp(() {
      ${className.toLowerCase()} = $className();
    });
    
    tearDown(() {
      // Cleanup
    });
    
    test('should initialize correctly', () {
      expect(${className.toLowerCase()}, isNotNull);
    });
    
    // TODO: Add specific tests for $className methods
    // TODO: Test error handling scenarios
    // TODO: Test edge cases
    // TODO: Add integration tests if needed
  });
}
''';
  }
  
  /// Extract class name from file path
  static String _extractClassName(String filePath) {
    final fileName = filePath.split('/').last;
    final nameWithoutExt = fileName.split('.').first;
    
    // Convert snake_case to PascalCase
    return nameWithoutExt
        .split('_')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join('');
  }
  
  /// Print coverage report
  static void printReport(CoverageReport report) {
    print('\n🧪 CarNow Coverage Analysis Report');
    print('=' * 50);
    print('Overall Coverage: ${report.overallCoverage.toStringAsFixed(2)}%');
    print('Target Coverage: ${targetCoverage.toStringAsFixed(1)}%');
    print('Files Analyzed: ${report.files.length}');
    print('');
    
    // Files below target
    final belowTarget = report.files.where((f) => f.coverage < targetCoverage).toList();
    if (belowTarget.isNotEmpty) {
      print('📉 Files Below Target Coverage:');
      for (final file in belowTarget) {
        print('  ${file.path}: ${file.coverage.toStringAsFixed(1)}%');
      }
      print('');
    }
    
    // Top covered files
    final topFiles = report.files.where((f) => f.coverage >= targetCoverage).toList();
    if (topFiles.isNotEmpty) {
      print('✅ Well Covered Files:');
      for (final file in topFiles.take(5)) {
        print('  ${file.path}: ${file.coverage.toStringAsFixed(1)}%');
      }
    }
  }
}

class CoverageReport {
  final List<FileCoverage> files = [];
  
  void addFile(String path, double coverage, int totalLines, int coveredLines) {
    files.add(FileCoverage(
      path: path,
      coverage: coverage,
      totalLines: totalLines,
      coveredLines: coveredLines,
    ));
  }
  
  double get overallCoverage {
    if (files.isEmpty) return 0.0;
    
    final totalLines = files.fold(0, (sum, file) => sum + file.totalLines);
    final coveredLines = files.fold(0, (sum, file) => sum + file.coveredLines);
    
    return totalLines > 0 ? (coveredLines / totalLines) * 100 : 0.0;
  }
}

class FileCoverage {
  final String path;
  final double coverage;
  final int totalLines;
  final int coveredLines;
  
  FileCoverage({
    required this.path,
    required this.coverage,
    required this.totalLines,
    required this.coveredLines,
  });
}

// Main function to run coverage analysis
void main() async {
  try {
    print('🔍 Analyzing test coverage...');
    final report = await CoverageAnalyzer.analyzeCoverage();
    
    CoverageAnalyzer.printReport(report);
    
    // Generate missing tests
    final missingTests = await CoverageAnalyzer.generateMissingTests(report);
    if (missingTests.isNotEmpty) {
      print('\n📝 Missing Test Files:');
      for (final testFile in missingTests) {
        print('  $testFile');
      }
    }
    
    print('\n🎯 Next Steps:');
    print('1. Create missing test files');
    print('2. Add comprehensive test cases');
    print('3. Run tests to verify coverage improvement');
    print('4. Aim for ${CoverageAnalyzer.targetCoverage}% overall coverage');
    
  } catch (e) {
    print('❌ Error analyzing coverage: $e');
    exit(1);
  }
}
