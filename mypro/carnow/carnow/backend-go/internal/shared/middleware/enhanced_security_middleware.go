// =============================================================================
// CARNOW ENHANCED SECURITY MIDDLEWARE - Stage 3 Implementation
// =============================================================================
//
// This middleware implements advanced security features for CarNow following
// Forever Plan Architecture principles with enterprise-grade security.
//
// Features:
// - Real-time threat detection and blocking
// - Advanced security headers optimization
// - Request sanitization and validation
// - Security event logging and monitoring
// - Automated incident response
//
// Architecture: Flutter (UI Only) → Go API (Enhanced Security) → Supabase (Data Only)

package middleware

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"carnow/internal/shared/services"
)

// EnhancedSecurityMiddlewareConfig holds configuration for enhanced security middleware
type EnhancedSecurityMiddlewareConfig struct {
	EnableThreatDetection     bool          `json:"enable_threat_detection"`
	EnableSecurityHeaders     bool          `json:"enable_security_headers"`
	EnableRequestSanitization bool          `json:"enable_request_sanitization"`
	EnableRealTimeBlocking    bool          `json:"enable_real_time_blocking"`
	MaxRequestBodySize        int64         `json:"max_request_body_size"`
	RequestTimeout            time.Duration `json:"request_timeout"`
	Logger                    *zap.Logger   `json:"-"`
}

// DefaultEnhancedSecurityConfig returns default configuration
func DefaultEnhancedSecurityConfig() *EnhancedSecurityMiddlewareConfig {
	return &EnhancedSecurityMiddlewareConfig{
		EnableThreatDetection:     true,
		EnableSecurityHeaders:     true,
		EnableRequestSanitization: true,
		EnableRealTimeBlocking:    true,
		MaxRequestBodySize:        10 * 1024 * 1024, // 10MB
		RequestTimeout:            30 * time.Second,
	}
}

// EnhancedSecurityMiddleware creates enhanced security middleware
func EnhancedSecurityMiddleware(securityService *services.EnhancedSecurityService, config *EnhancedSecurityMiddlewareConfig) gin.HandlerFunc {
	if config == nil {
		config = DefaultEnhancedSecurityConfig()
	}

	return func(c *gin.Context) {
		startTime := time.Now()
		clientIP := getClientIP(c)
		userAgent := c.GetHeader("User-Agent")
		requestPath := c.Request.URL.Path

		// Set request timeout
		if config.RequestTimeout > 0 {
			ctx, cancel := context.WithTimeout(c.Request.Context(), config.RequestTimeout)
			defer cancel()
			c.Request = c.Request.WithContext(ctx)
		}

		// Apply enhanced security headers
		if config.EnableSecurityHeaders {
			applyEnhancedSecurityHeaders(c)
		}

		// Check if IP is blocked by security service
		if config.EnableRealTimeBlocking && securityService.IsIPBlocked(clientIP) {
			config.Logger.Warn("Blocked IP attempted access",
				zap.String("client_ip", clientIP),
				zap.String("path", requestPath),
				zap.String("user_agent", userAgent),
			)
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"error":   "Access denied",
				"code":    "IP_BLOCKED",
				"message": "Your IP address has been blocked due to security violations",
			})
			c.Abort()
			return
		}

		// Read and analyze request body for threats
		var requestBody string
		if config.EnableThreatDetection && c.Request.Body != nil {
			bodyBytes, err := io.ReadAll(c.Request.Body)
			if err == nil {
				requestBody = string(bodyBytes)
				// Restore the body for downstream handlers
				c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
			}
		}

		// Perform threat analysis
		if config.EnableThreatDetection {
			event, err := securityService.AnalyzeThreat(
				c.Request.Context(),
				clientIP,
				userAgent,
				requestPath,
				requestBody,
			)

			if err != nil {
				config.Logger.Error("Threat analysis failed",
					zap.String("client_ip", clientIP),
					zap.Error(err),
				)
			}

			if event != nil {
				// Log security event
				config.Logger.Warn("Security threat detected",
					zap.String("event_id", event.ID),
					zap.String("threat_type", event.Type),
					zap.String("client_ip", clientIP),
					zap.Int("threat_level", int(event.Level)),
				)

				// Block high-level threats immediately
				if event.Level >= services.ThreatLevelHigh {
					c.JSON(http.StatusForbidden, gin.H{
						"success": false,
						"error":   "Security violation detected",
						"code":    "SECURITY_THREAT",
						"message": "Request blocked due to security policy violation",
					})
					c.Abort()
					return
				}
			}
		}

		// Sanitize request if enabled
		if config.EnableRequestSanitization {
			sanitizeRequest(c)
		}

		// Validate request size
		if config.MaxRequestBodySize > 0 && c.Request.ContentLength > config.MaxRequestBodySize {
			config.Logger.Warn("Request size exceeded limit",
				zap.String("client_ip", clientIP),
				zap.Int64("content_length", c.Request.ContentLength),
				zap.Int64("max_size", config.MaxRequestBodySize),
			)
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{
				"success": false,
				"error":   "Request too large",
				"code":    "REQUEST_TOO_LARGE",
				"message": "Request size exceeds maximum allowed limit",
			})
			c.Abort()
			return
		}

		// Continue to next handler
		c.Next()

		// Log request completion
		duration := time.Since(startTime)
		config.Logger.Info("Request processed",
			zap.String("client_ip", clientIP),
			zap.String("method", c.Request.Method),
			zap.String("path", requestPath),
			zap.Int("status", c.Writer.Status()),
			zap.Duration("duration", duration),
		)
	}
}

// applyEnhancedSecurityHeaders applies comprehensive security headers
func applyEnhancedSecurityHeaders(c *gin.Context) {
	// Content Security Policy (Enhanced)
	csp := "default-src 'self'; " +
		"script-src 'self' 'unsafe-inline' https://apis.google.com https://www.gstatic.com; " +
		"style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
		"font-src 'self' https://fonts.gstatic.com; " +
		"img-src 'self' data: https: blob:; " +
		"connect-src 'self' https://api.carnow.app https://lpxtghyvxuenyyisrrro.supabase.co; " +
		"frame-src 'none'; " +
		"object-src 'none'; " +
		"base-uri 'self'; " +
		"form-action 'self'; " +
		"upgrade-insecure-requests; " +
		"block-all-mixed-content;"

	c.Header("Content-Security-Policy", csp)

	// Strict Transport Security (Enhanced)
	c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload")

	// X-Frame-Options
	c.Header("X-Frame-Options", "DENY")

	// X-Content-Type-Options
	c.Header("X-Content-Type-Options", "nosniff")

	// X-XSS-Protection
	c.Header("X-XSS-Protection", "1; mode=block")

	// Referrer Policy (Enhanced)
	c.Header("Referrer-Policy", "strict-origin-when-cross-origin")

	// Permissions Policy (Enhanced)
	permissionsPolicy := "geolocation=(), microphone=(), camera=(), " +
		"payment=(), usb=(), magnetometer=(), gyroscope=(), " +
		"accelerometer=(), ambient-light-sensor=(), autoplay=(), " +
		"encrypted-media=(), fullscreen=(), picture-in-picture=()"
	c.Header("Permissions-Policy", permissionsPolicy)

	// Feature Policy (Fallback for older browsers)
	c.Header("Feature-Policy", "geolocation 'none'; microphone 'none'; camera 'none'")

	// Cross-Origin Policies
	c.Header("Cross-Origin-Embedder-Policy", "require-corp")
	c.Header("Cross-Origin-Opener-Policy", "same-origin")
	c.Header("Cross-Origin-Resource-Policy", "same-origin")

	// Cache Control for sensitive endpoints
	if isSensitiveEndpoint(c.Request.URL.Path) {
		c.Header("Cache-Control", "no-store, no-cache, must-revalidate, private")
		c.Header("Pragma", "no-cache")
		c.Header("Expires", "0")
	}

	// Remove server information
	c.Header("Server", "")
	c.Header("X-Powered-By", "")

	// Add security response headers
	c.Header("X-Security-Framework", "CarNow-Enhanced-Security")
	c.Header("X-Content-Security-Policy", csp) // Fallback for older browsers
}

// sanitizeRequest sanitizes request parameters and headers
func sanitizeRequest(c *gin.Context) {
	// Sanitize query parameters
	for key, values := range c.Request.URL.Query() {
		for i, value := range values {
			c.Request.URL.Query()[key][i] = sanitizeString(value)
		}
	}

	// Sanitize headers (specific ones that might contain user input)
	sensitiveHeaders := []string{
		"X-Forwarded-For",
		"X-Real-IP",
		"X-Client-IP",
		"User-Agent",
		"Referer",
	}

	for _, header := range sensitiveHeaders {
		if value := c.GetHeader(header); value != "" {
			c.Request.Header.Set(header, sanitizeString(value))
		}
	}
}

// sanitizeString removes potentially dangerous characters from strings
func sanitizeString(input string) string {
	// Remove null bytes
	input = strings.ReplaceAll(input, "\x00", "")

	// Remove control characters except tab, newline, and carriage return
	var result strings.Builder
	for _, r := range input {
		if r >= 32 || r == 9 || r == 10 || r == 13 {
			result.WriteRune(r)
		}
	}

	return result.String()
}

// isSensitiveEndpoint checks if an endpoint contains sensitive data
func isSensitiveEndpoint(path string) bool {
	sensitivePatterns := []string{
		"/auth/",
		"/admin/",
		"/user/",
		"/profile/",
		"/payment/",
		"/api/v1/auth/",
		"/api/v1/admin/",
		"/api/v1/users/",
	}

	for _, pattern := range sensitivePatterns {
		if strings.Contains(path, pattern) {
			return true
		}
	}

	return false
}

// getClientIP extracts the real client IP address
func getClientIP(c *gin.Context) string {
	// Check X-Forwarded-For header
	if xff := c.GetHeader("X-Forwarded-For"); xff != "" {
		// Take the first IP in the chain
		ips := strings.Split(xff, ",")
		if len(ips) > 0 {
			return strings.TrimSpace(ips[0])
		}
	}

	// Check X-Real-IP header
	if xri := c.GetHeader("X-Real-IP"); xri != "" {
		return strings.TrimSpace(xri)
	}

	// Check X-Client-IP header
	if xci := c.GetHeader("X-Client-IP"); xci != "" {
		return strings.TrimSpace(xci)
	}

	// Fallback to RemoteAddr
	return c.ClientIP()
}

// SecurityHeadersOnlyMiddleware applies only security headers (for public endpoints)
func SecurityHeadersOnlyMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		applyEnhancedSecurityHeaders(c)
		c.Next()
	}
}

// CORSSecurityMiddleware applies CORS with security considerations
func CORSSecurityMiddleware(allowedOrigins []string) gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.GetHeader("Origin")
		
		// Check if origin is allowed
		allowed := false
		for _, allowedOrigin := range allowedOrigins {
			if allowedOrigin == "*" || allowedOrigin == origin {
				allowed = true
				break
			}
		}

		if allowed {
			if origin != "" {
				c.Header("Access-Control-Allow-Origin", origin)
			} else {
				c.Header("Access-Control-Allow-Origin", allowedOrigins[0])
			}
			
			c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, PATCH, OPTIONS")
			c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Accept, Authorization, X-Requested-With, X-API-Key, X-Client-Version, X-Device-ID")
			c.Header("Access-Control-Expose-Headers", "X-Total-Count, X-Rate-Limit-Remaining, X-Rate-Limit-Reset")
			c.Header("Access-Control-Allow-Credentials", "true")
			c.Header("Access-Control-Max-Age", "3600")
		}

		// Handle preflight requests
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// RequestIDMiddleware adds a unique request ID for tracking
func RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := generateRequestID()
		c.Header("X-Request-ID", requestID)
		c.Set("request_id", requestID)
		c.Next()
	}
}

// generateRequestID generates a unique request ID
func generateRequestID() string {
	return fmt.Sprintf("%d-%d", time.Now().UnixNano(), time.Now().Nanosecond())
}
