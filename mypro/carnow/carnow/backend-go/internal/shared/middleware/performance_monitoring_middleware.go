// =============================================================================
// CARNOW PERFORMANCE MONITORING MIDDLEWARE - Stage 4 Implementation
// =============================================================================
//
// This middleware provides comprehensive performance monitoring for CarNow
// following Forever Plan Architecture principles with enterprise-grade monitoring.
//
// Features:
// - Request/response time tracking
// - Database query performance monitoring
// - Memory usage tracking
// - Throughput monitoring
// - Performance alerting
//
// Architecture: Flutter (UI Only) → Go API (Performance Monitored) → Supabase (Data Only)

package middleware

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"carnow/internal/shared/services"
)

// PerformanceMonitoringConfig holds configuration for performance monitoring middleware
type PerformanceMonitoringConfig struct {
	EnableRequestTracking    bool          `json:"enable_request_tracking"`
	EnableResponseTracking   bool          `json:"enable_response_tracking"`
	EnableMemoryTracking     bool          `json:"enable_memory_tracking"`
	EnableDatabaseTracking   bool          `json:"enable_database_tracking"`
	SlowRequestThreshold     time.Duration `json:"slow_request_threshold"`
	LargeResponseThreshold   int64         `json:"large_response_threshold"`
	Logger                   *zap.Logger   `json:"-"`
}

// DefaultPerformanceMonitoringConfig returns default configuration
func DefaultPerformanceMonitoringConfig() *PerformanceMonitoringConfig {
	return &PerformanceMonitoringConfig{
		EnableRequestTracking:    true,
		EnableResponseTracking:   true,
		EnableMemoryTracking:     true,
		EnableDatabaseTracking:   true,
		SlowRequestThreshold:     500 * time.Millisecond,
		LargeResponseThreshold:   1024 * 1024, // 1MB
	}
}

// RequestMetrics holds metrics for a single request
type RequestMetrics struct {
	Method           string        `json:"method"`
	Path             string        `json:"path"`
	StatusCode       int           `json:"status_code"`
	Duration         time.Duration `json:"duration"`
	RequestSize      int64         `json:"request_size"`
	ResponseSize     int64         `json:"response_size"`
	ClientIP         string        `json:"client_ip"`
	UserAgent        string        `json:"user_agent"`
	Timestamp        time.Time     `json:"timestamp"`
	DatabaseQueries  int           `json:"database_queries"`
	DatabaseDuration time.Duration `json:"database_duration"`
	CacheHits        int           `json:"cache_hits"`
	CacheMisses      int           `json:"cache_misses"`
}

// responseWriter wraps gin.ResponseWriter to capture response size
type responseWriter struct {
	gin.ResponseWriter
	size int64
}

func (w *responseWriter) Write(data []byte) (int, error) {
	size, err := w.ResponseWriter.Write(data)
	w.size += int64(size)
	return size, err
}

func (w *responseWriter) WriteString(s string) (int, error) {
	size, err := w.ResponseWriter.WriteString(s)
	w.size += int64(size)
	return size, err
}

// PerformanceMonitoringMiddleware creates performance monitoring middleware
func PerformanceMonitoringMiddleware(
	performanceService *services.PerformanceOptimizationService,
	config *PerformanceMonitoringConfig,
) gin.HandlerFunc {
	if config == nil {
		config = DefaultPerformanceMonitoringConfig()
	}

	return func(c *gin.Context) {
		startTime := time.Now()

		// Wrap response writer to capture response size
		wrapped := &responseWriter{
			ResponseWriter: c.Writer,
			size:          0,
		}
		c.Writer = wrapped

		// Get request size
		var requestSize int64
		if c.Request.Body != nil && config.EnableRequestTracking {
			bodyBytes, err := io.ReadAll(c.Request.Body)
			if err == nil {
				requestSize = int64(len(bodyBytes))
				// Restore the body for downstream handlers
				c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
			}
		}

		// Set performance tracking context
		ctx := context.WithValue(c.Request.Context(), "performance_start_time", startTime)
		ctx = context.WithValue(ctx, "database_queries", 0)
		ctx = context.WithValue(ctx, "database_duration", time.Duration(0))
		ctx = context.WithValue(ctx, "cache_hits", 0)
		ctx = context.WithValue(ctx, "cache_misses", 0)
		c.Request = c.Request.WithContext(ctx)

		// Process request
		c.Next()

		// Calculate metrics
		duration := time.Since(startTime)
		responseSize := wrapped.size

		// Extract performance data from context
		databaseQueries := getIntFromContext(c.Request.Context(), "database_queries")
		databaseDuration := getDurationFromContext(c.Request.Context(), "database_duration")
		cacheHits := getIntFromContext(c.Request.Context(), "cache_hits")
		cacheMisses := getIntFromContext(c.Request.Context(), "cache_misses")

		// Create request metrics
		metrics := RequestMetrics{
			Method:           c.Request.Method,
			Path:             c.Request.URL.Path,
			StatusCode:       c.Writer.Status(),
			Duration:         duration,
			RequestSize:      requestSize,
			ResponseSize:     responseSize,
			ClientIP:         c.ClientIP(),
			UserAgent:        c.GetHeader("User-Agent"),
			Timestamp:        startTime,
			DatabaseQueries:  databaseQueries,
			DatabaseDuration: databaseDuration,
			CacheHits:        cacheHits,
			CacheMisses:      cacheMisses,
		}

		// Record performance metrics
		success := c.Writer.Status() < 400
		performanceService.RecordRequestPerformance(duration, success)

		// Log performance data
		logPerformanceMetrics(config.Logger, metrics, config)

		// Check for performance issues
		checkPerformanceIssues(config.Logger, metrics, config)

		// Add performance headers
		addPerformanceHeaders(c, metrics)
	}
}

// DatabaseQueryMiddleware tracks database query performance
func DatabaseQueryMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// This middleware would be used to wrap database operations
		// and track query performance
		c.Next()
	}
}

// CachePerformanceMiddleware tracks cache performance
func CachePerformanceMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// This middleware would be used to track cache hits/misses
		c.Next()
	}
}

// logPerformanceMetrics logs performance metrics
func logPerformanceMetrics(logger *zap.Logger, metrics RequestMetrics, config *PerformanceMonitoringConfig) {
	if logger == nil {
		return
	}

	fields := []zap.Field{
		zap.String("method", metrics.Method),
		zap.String("path", metrics.Path),
		zap.Int("status", metrics.StatusCode),
		zap.Duration("duration", metrics.Duration),
		zap.String("client_ip", metrics.ClientIP),
	}

	if config.EnableRequestTracking {
		fields = append(fields,
			zap.Int64("request_size", metrics.RequestSize),
		)
	}

	if config.EnableResponseTracking {
		fields = append(fields,
			zap.Int64("response_size", metrics.ResponseSize),
		)
	}

	if config.EnableDatabaseTracking {
		fields = append(fields,
			zap.Int("db_queries", metrics.DatabaseQueries),
			zap.Duration("db_duration", metrics.DatabaseDuration),
		)
	}

	// Log at appropriate level based on performance
	if metrics.Duration > config.SlowRequestThreshold {
		logger.Warn("Slow request detected", fields...)
	} else if metrics.StatusCode >= 400 {
		logger.Warn("Request failed", fields...)
	} else {
		logger.Info("Request completed", fields...)
	}
}

// checkPerformanceIssues checks for various performance issues
func checkPerformanceIssues(logger *zap.Logger, metrics RequestMetrics, config *PerformanceMonitoringConfig) {
	if logger == nil {
		return
	}

	// Check for slow requests
	if metrics.Duration > config.SlowRequestThreshold {
		logger.Warn("Performance issue: Slow request",
			zap.String("path", metrics.Path),
			zap.Duration("duration", metrics.Duration),
			zap.Duration("threshold", config.SlowRequestThreshold),
		)
	}

	// Check for large responses
	if config.EnableResponseTracking && metrics.ResponseSize > config.LargeResponseThreshold {
		logger.Warn("Performance issue: Large response",
			zap.String("path", metrics.Path),
			zap.Int64("response_size", metrics.ResponseSize),
			zap.Int64("threshold", config.LargeResponseThreshold),
		)
	}

	// Check for excessive database queries
	if config.EnableDatabaseTracking && metrics.DatabaseQueries > 10 {
		logger.Warn("Performance issue: Excessive database queries",
			zap.String("path", metrics.Path),
			zap.Int("db_queries", metrics.DatabaseQueries),
		)
	}

	// Check for slow database operations
	if config.EnableDatabaseTracking && metrics.DatabaseDuration > 100*time.Millisecond {
		logger.Warn("Performance issue: Slow database operations",
			zap.String("path", metrics.Path),
			zap.Duration("db_duration", metrics.DatabaseDuration),
		)
	}

	// Check for poor cache performance
	totalCacheOperations := metrics.CacheHits + metrics.CacheMisses
	if totalCacheOperations > 0 {
		hitRate := float64(metrics.CacheHits) / float64(totalCacheOperations)
		if hitRate < 0.8 { // Less than 80% hit rate
			logger.Warn("Performance issue: Poor cache hit rate",
				zap.String("path", metrics.Path),
				zap.Float64("hit_rate", hitRate),
				zap.Int("cache_hits", metrics.CacheHits),
				zap.Int("cache_misses", metrics.CacheMisses),
			)
		}
	}
}

// addPerformanceHeaders adds performance-related headers to the response
func addPerformanceHeaders(c *gin.Context, metrics RequestMetrics) {
	// Add response time header
	c.Header("X-Response-Time", metrics.Duration.String())

	// Add request ID if available
	if requestID, exists := c.Get("request_id"); exists {
		c.Header("X-Request-ID", requestID.(string))
	}

	// Add performance metrics headers (for debugging)
	if gin.Mode() == gin.DebugMode {
		c.Header("X-DB-Queries", strconv.Itoa(metrics.DatabaseQueries))
		c.Header("X-DB-Duration", metrics.DatabaseDuration.String())
		c.Header("X-Cache-Hits", strconv.Itoa(metrics.CacheHits))
		c.Header("X-Cache-Misses", strconv.Itoa(metrics.CacheMisses))
	}
}

// Helper functions to extract values from context
func getIntFromContext(ctx context.Context, key string) int {
	if value := ctx.Value(key); value != nil {
		if intValue, ok := value.(int); ok {
			return intValue
		}
	}
	return 0
}

func getDurationFromContext(ctx context.Context, key string) time.Duration {
	if value := ctx.Value(key); value != nil {
		if durationValue, ok := value.(time.Duration); ok {
			return durationValue
		}
	}
	return 0
}

// IncrementDatabaseQueries increments the database query counter in context
func IncrementDatabaseQueries(ctx context.Context, duration time.Duration) context.Context {
	queries := getIntFromContext(ctx, "database_queries")
	totalDuration := getDurationFromContext(ctx, "database_duration")

	ctx = context.WithValue(ctx, "database_queries", queries+1)
	ctx = context.WithValue(ctx, "database_duration", totalDuration+duration)

	return ctx
}

// IncrementCacheHits increments the cache hit counter in context
func IncrementCacheHits(ctx context.Context) context.Context {
	hits := getIntFromContext(ctx, "cache_hits")
	return context.WithValue(ctx, "cache_hits", hits+1)
}

// IncrementCacheMisses increments the cache miss counter in context
func IncrementCacheMisses(ctx context.Context) context.Context {
	misses := getIntFromContext(ctx, "cache_misses")
	return context.WithValue(ctx, "cache_misses", misses+1)
}

// PerformanceMetricsMiddleware provides endpoint for performance metrics
func PerformanceMetricsMiddleware(performanceService *services.PerformanceOptimizationService) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.URL.Path == "/metrics/performance" {
			metrics := performanceService.GetPerformanceMetrics()
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"data":    metrics,
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

// HealthCheckMiddleware provides health check endpoint with performance data
func HealthCheckMiddleware(performanceService *services.PerformanceOptimizationService) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.URL.Path == "/health/performance" {
			metrics := performanceService.GetPerformanceMetrics()
			
			// Determine health status based on metrics
			status := "healthy"
			if metrics.AverageResponseTime > 500*time.Millisecond {
				status = "degraded"
			}
			if metrics.AverageResponseTime > 1*time.Second {
				status = "unhealthy"
			}

			c.JSON(http.StatusOK, gin.H{
				"status": status,
				"performance": gin.H{
					"average_response_time": metrics.AverageResponseTime.String(),
					"requests_per_second":   metrics.RequestsPerSecond,
					"memory_usage_mb":       metrics.MemoryUsage / 1024 / 1024,
					"goroutines":           metrics.Goroutines,
				},
				"timestamp": time.Now(),
			})
			c.Abort()
			return
		}
		c.Next()
	}
}
