// =============================================================================
// CARNOW PERFORMANCE HANDLERS - Stage 4 Implementation
// =============================================================================
//
// This file implements performance-related HTTP handlers for CarNow following
// Forever Plan Architecture principles with enterprise-grade performance monitoring.
//
// Features:
// - Performance metrics endpoints
// - Performance dashboard data
// - Query optimization suggestions
// - Performance alerts management
// - System health monitoring
//
// Architecture: Flutter (UI Only) → Go API (Performance Handlers) → Supabase (Data Only)

package handlers

import (
	"database/sql"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"carnow/internal/shared/services"
)

// PerformanceHandlers handles performance-related HTTP requests
type PerformanceHandlers struct {
	logger             *zap.Logger
	performanceService *services.PerformanceOptimizationService
	db                 *sql.DB
}

// NewPerformanceHandlers creates new performance handlers
func NewPerformanceHandlers(
	logger *zap.Logger,
	performanceService *services.PerformanceOptimizationService,
	db *sql.DB,
) *PerformanceHandlers {
	return &PerformanceHandlers{
		logger:             logger,
		performanceService: performanceService,
		db:                 db,
	}
}

// GetPerformanceMetrics returns comprehensive performance metrics
// GET /api/v1/admin/performance/metrics
func (h *PerformanceHandlers) GetPerformanceMetrics(c *gin.Context) {
	h.logger.Info("Performance metrics requested",
		zap.String("client_ip", c.ClientIP()),
		zap.String("user_agent", c.GetHeader("User-Agent")),
	)

	metrics := h.performanceService.GetPerformanceMetrics()

	// Add database connection stats if available
	var dbStats map[string]interface{}
	if h.db != nil {
		dbStats = h.performanceService.GetDatabaseConnectionStats(h.db)
	}

	response := gin.H{
		"success": true,
		"data": gin.H{
			"performance_metrics": metrics,
			"database_stats":      dbStats,
			"timestamp":          time.Now(),
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetPerformanceDashboard returns performance dashboard data
// GET /api/v1/admin/performance/dashboard
func (h *PerformanceHandlers) GetPerformanceDashboard(c *gin.Context) {
	h.logger.Info("Performance dashboard requested",
		zap.String("client_ip", c.ClientIP()),
	)

	metrics := h.performanceService.GetPerformanceMetrics()
	slowQueries := h.performanceService.GetSlowQueries(10)
	alerts := h.performanceService.GetPerformanceAlerts(20)

	// Calculate performance score
	performanceScore := h.calculatePerformanceScore(metrics)

	// Get database connection stats
	var dbStats map[string]interface{}
	if h.db != nil {
		dbStats = h.performanceService.GetDatabaseConnectionStats(h.db)
	}

	dashboard := gin.H{
		"performance_score": performanceScore,
		"metrics":          metrics,
		"slow_queries":     slowQueries,
		"recent_alerts":    alerts,
		"database_stats":   dbStats,
		"system_health": gin.H{
			"status":      h.getSystemHealthStatus(metrics),
			"uptime":      time.Since(time.Now().Add(-24 * time.Hour)).String(), // Placeholder
			"last_check":  time.Now(),
		},
		"recommendations": h.getPerformanceRecommendations(metrics),
		"timestamp":       time.Now(),
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    dashboard,
	})
}

// GetSlowQueries returns slow query analysis
// GET /api/v1/admin/performance/slow-queries
func (h *PerformanceHandlers) GetSlowQueries(c *gin.Context) {
	h.logger.Info("Slow queries requested",
		zap.String("client_ip", c.ClientIP()),
	)

	limitStr := c.DefaultQuery("limit", "50")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid limit parameter",
		})
		return
	}

	slowQueries := h.performanceService.GetSlowQueries(limit)

	// Add optimization suggestions for each query
	queriesWithSuggestions := make([]gin.H, 0, len(slowQueries))
	for _, query := range slowQueries {
		optimization := h.performanceService.OptimizeQuery(query.Query)
		queriesWithSuggestions = append(queriesWithSuggestions, gin.H{
			"query_data":    query,
			"optimization": optimization,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"slow_queries": queriesWithSuggestions,
			"total":        len(slowQueries),
		},
	})
}

// OptimizeQuery provides query optimization suggestions
// POST /api/v1/admin/performance/optimize-query
func (h *PerformanceHandlers) OptimizeQuery(c *gin.Context) {
	var request struct {
		Query string `json:"query" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	h.logger.Info("Query optimization requested",
		zap.String("client_ip", c.ClientIP()),
		zap.String("query_preview", request.Query[:min(50, len(request.Query))]),
	)

	optimization := h.performanceService.OptimizeQuery(request.Query)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    optimization,
	})
}

// GetPerformanceAlerts returns performance alerts
// GET /api/v1/admin/performance/alerts
func (h *PerformanceHandlers) GetPerformanceAlerts(c *gin.Context) {
	h.logger.Info("Performance alerts requested",
		zap.String("client_ip", c.ClientIP()),
	)

	limitStr := c.DefaultQuery("limit", "100")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid limit parameter",
		})
		return
	}

	alerts := h.performanceService.GetPerformanceAlerts(limit)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"alerts": alerts,
			"total":  len(alerts),
		},
	})
}

// TriggerMemoryOptimization triggers memory optimization
// POST /api/v1/admin/performance/optimize-memory
func (h *PerformanceHandlers) TriggerMemoryOptimization(c *gin.Context) {
	// Get user information from context
	userEmail, exists := c.Get("user_email")
	if !exists {
		userEmail = "unknown"
	}

	h.logger.Info("Memory optimization triggered",
		zap.String("triggered_by", userEmail.(string)),
		zap.String("client_ip", c.ClientIP()),
	)

	// Get metrics before optimization
	metricsBefore := h.performanceService.GetPerformanceMetrics()

	// Trigger memory optimization
	h.performanceService.OptimizeMemory()

	// Wait a moment for GC to complete
	time.Sleep(100 * time.Millisecond)

	// Get metrics after optimization
	metricsAfter := h.performanceService.GetPerformanceMetrics()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"memory_before": metricsBefore.MemoryUsage,
			"memory_after":  metricsAfter.MemoryUsage,
			"memory_freed":  int64(metricsBefore.MemoryUsage) - int64(metricsAfter.MemoryUsage),
			"timestamp":     time.Now(),
		},
		"message": "Memory optimization completed successfully",
	})
}

// GetSystemHealth returns comprehensive system health
// GET /api/v1/admin/performance/health
func (h *PerformanceHandlers) GetSystemHealth(c *gin.Context) {
	h.logger.Info("System health requested",
		zap.String("client_ip", c.ClientIP()),
	)

	metrics := h.performanceService.GetPerformanceMetrics()
	
	// Get database connection stats
	var dbStats map[string]interface{}
	if h.db != nil {
		dbStats = h.performanceService.GetDatabaseConnectionStats(h.db)
	}

	health := gin.H{
		"overall_status": h.getSystemHealthStatus(metrics),
		"components": gin.H{
			"api": gin.H{
				"status":               h.getAPIHealthStatus(metrics),
				"average_response_time": metrics.AverageResponseTime.String(),
				"requests_per_second":   metrics.RequestsPerSecond,
				"error_rate":           h.calculateErrorRate(metrics),
			},
			"database": gin.H{
				"status":              h.getDatabaseHealthStatus(dbStats),
				"connections":         dbStats,
				"average_query_time":  metrics.AverageQueryTime.String(),
				"slow_queries":        metrics.SlowQueries,
			},
			"memory": gin.H{
				"status":      h.getMemoryHealthStatus(metrics),
				"usage_bytes": metrics.MemoryUsage,
				"usage_mb":    metrics.MemoryUsage / 1024 / 1024,
				"heap_size":   metrics.HeapSize,
				"goroutines":  metrics.Goroutines,
			},
		},
		"performance_score": h.calculatePerformanceScore(metrics),
		"last_updated":      metrics.LastUpdated,
		"uptime":           time.Since(time.Now().Add(-24 * time.Hour)).String(), // Placeholder
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    health,
	})
}

// GetPerformanceReport generates a comprehensive performance report
// GET /api/v1/admin/performance/report
func (h *PerformanceHandlers) GetPerformanceReport(c *gin.Context) {
	h.logger.Info("Performance report requested",
		zap.String("client_ip", c.ClientIP()),
	)

	periodStr := c.DefaultQuery("period", "24h")
	period, err := time.ParseDuration(periodStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid period parameter",
		})
		return
	}

	metrics := h.performanceService.GetPerformanceMetrics()
	slowQueries := h.performanceService.GetSlowQueries(20)
	alerts := h.performanceService.GetPerformanceAlerts(50)

	// Get database connection stats
	var dbStats map[string]interface{}
	if h.db != nil {
		dbStats = h.performanceService.GetDatabaseConnectionStats(h.db)
	}

	report := gin.H{
		"period":           periodStr,
		"generated_at":     time.Now(),
		"performance_metrics": metrics,
		"database_stats":   dbStats,
		"slow_queries":     slowQueries,
		"performance_alerts": alerts,
		"summary": gin.H{
			"performance_score":     h.calculatePerformanceScore(metrics),
			"average_response_time": metrics.AverageResponseTime.String(),
			"requests_per_second":   metrics.RequestsPerSecond,
			"total_requests":        metrics.TotalRequests,
			"failed_requests":       metrics.FailedRequests,
			"slow_queries_count":    metrics.SlowQueries,
			"memory_usage_mb":       metrics.MemoryUsage / 1024 / 1024,
		},
		"recommendations": h.getPerformanceRecommendations(metrics),
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    report,
	})
}

// Helper functions

func (h *PerformanceHandlers) calculatePerformanceScore(metrics services.PerformanceMetrics) float64 {
	score := 100.0

	// Deduct points for slow response times
	if metrics.AverageResponseTime > 100*time.Millisecond {
		score -= 10.0
	}
	if metrics.AverageResponseTime > 500*time.Millisecond {
		score -= 20.0
	}
	if metrics.AverageResponseTime > 1*time.Second {
		score -= 30.0
	}

	// Deduct points for failed requests
	if metrics.TotalRequests > 0 {
		errorRate := float64(metrics.FailedRequests) / float64(metrics.TotalRequests)
		score -= errorRate * 50.0
	}

	// Deduct points for slow queries
	if metrics.SlowQueries > 0 {
		score -= float64(metrics.SlowQueries) * 0.5
	}

	// Ensure score is between 0 and 100
	if score < 0 {
		score = 0
	}

	return score
}

func (h *PerformanceHandlers) getSystemHealthStatus(metrics services.PerformanceMetrics) string {
	score := h.calculatePerformanceScore(metrics)
	
	if score >= 90 {
		return "excellent"
	} else if score >= 75 {
		return "good"
	} else if score >= 60 {
		return "fair"
	} else if score >= 40 {
		return "poor"
	}
	return "critical"
}

func (h *PerformanceHandlers) getAPIHealthStatus(metrics services.PerformanceMetrics) string {
	if metrics.AverageResponseTime < 100*time.Millisecond {
		return "excellent"
	} else if metrics.AverageResponseTime < 500*time.Millisecond {
		return "good"
	} else if metrics.AverageResponseTime < 1*time.Second {
		return "fair"
	}
	return "poor"
}

func (h *PerformanceHandlers) getDatabaseHealthStatus(dbStats map[string]interface{}) string {
	if dbStats == nil {
		return "unknown"
	}
	// Simplified health check based on connection stats
	return "good"
}

func (h *PerformanceHandlers) getMemoryHealthStatus(metrics services.PerformanceMetrics) string {
	// Simplified memory health check
	memoryMB := metrics.MemoryUsage / 1024 / 1024
	if memoryMB < 100 {
		return "excellent"
	} else if memoryMB < 500 {
		return "good"
	} else if memoryMB < 1000 {
		return "fair"
	}
	return "poor"
}

func (h *PerformanceHandlers) calculateErrorRate(metrics services.PerformanceMetrics) float64 {
	if metrics.TotalRequests == 0 {
		return 0.0
	}
	return float64(metrics.FailedRequests) / float64(metrics.TotalRequests) * 100.0
}

func (h *PerformanceHandlers) getPerformanceRecommendations(metrics services.PerformanceMetrics) []string {
	recommendations := make([]string, 0)

	if metrics.AverageResponseTime > 500*time.Millisecond {
		recommendations = append(recommendations, "Consider optimizing API response times")
	}

	if metrics.SlowQueries > 10 {
		recommendations = append(recommendations, "Review and optimize slow database queries")
	}

	if metrics.MemoryUsage > 1024*1024*1024 { // 1GB
		recommendations = append(recommendations, "Consider memory optimization and garbage collection tuning")
	}

	if len(recommendations) == 0 {
		recommendations = append(recommendations, "System performance is optimal")
	}

	return recommendations
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
